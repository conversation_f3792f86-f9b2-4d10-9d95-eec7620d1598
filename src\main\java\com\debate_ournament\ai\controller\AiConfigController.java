package com.debate_ournament.ai.controller;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.debate_ournament.ai.entity.AiConfig;
import com.debate_ournament.ai.entity.AiProvider;
import com.debate_ournament.ai.service.AiConfigService;
import com.debate_ournament.dto.common.ApiResponse;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * AI配置管理控制器
 * 提供AI配置的CRUD操作接口
 */
@RestController
@RequestMapping("/ai/configs")
public class AiConfigController {

    private final AiConfigService aiConfigService;

    public AiConfigController(AiConfigService aiConfigService) {
        this.aiConfigService = aiConfigService;
    }

    /**
     * 创建AI配置
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<AiConfig>> createConfig(
            @Valid @RequestBody ConfigCreateRequest request,
            Authentication authentication) {

        Long userId = getUserId(authentication);

        AiConfig config = new AiConfig();
        config.setProvider(request.getProvider());
        config.setConfigName(request.getConfigName());
        config.setApiKey(request.getApiKey());
        config.setBaseUrl(request.getBaseUrl());
        config.setModelName(request.getModelName());
        config.setSystemPrompt(request.getSystemPrompt());
        config.setTemperature(request.getTemperature());
        config.setMaxTokens(request.getMaxTokens());
        config.setEnabled(request.getEnabled());
        config.setIsDefault(request.getIsDefault());
        config.setCreatedBy(userId);

        AiConfig createdConfig = aiConfigService.createConfig(config);

        return ResponseEntity.ok(ApiResponse.success("AI配置创建成功", createdConfig));
    }

    /**
     * 更新AI配置
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<AiConfig>> updateConfig(
            @PathVariable Long id,
            @Valid @RequestBody ConfigUpdateRequest request) {

        AiConfig updatedConfig = aiConfigService.updateConfig(id, request);

        return ResponseEntity.ok(ApiResponse.success("AI配置更新成功", updatedConfig));
    }

    /**
     * 删除AI配置
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> deleteConfig(@PathVariable Long id) {

        aiConfigService.deleteConfig(id);

        return ResponseEntity.ok(ApiResponse.success("AI配置删除成功"));
    }

    /**
     * 获取AI配置详情
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<AiConfig>> getConfig(@PathVariable Long id) {

        AiConfig config = aiConfigService.getConfigById(id);

        return ResponseEntity.ok(ApiResponse.success("获取配置成功", config));
    }

    /**
     * 获取所有启用的AI配置（分页）
     */
    @GetMapping
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Page<AiConfig>>> getConfigs(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) AiProvider provider) {

        Pageable pageable = PageRequest.of(page, size);
        Page<AiConfig> configs = aiConfigService.getConfigs(pageable, provider);

        return ResponseEntity.ok(ApiResponse.success("获取配置列表成功", configs));
    }

    /**
     * 根据提供商获取配置列表
     */
    @GetMapping("/provider/{provider}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<AiConfig>>> getConfigsByProvider(
            @PathVariable AiProvider provider) {

        List<AiConfig> configs = aiConfigService.getConfigsByProvider(provider);

        return ResponseEntity.ok(ApiResponse.success("获取配置列表成功", configs));
    }

    /**
     * 获取默认配置
     */
    @GetMapping("/default")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<AiConfig>> getDefaultConfig() {

        AiConfig defaultConfig = aiConfigService.getDefaultConfig();

        return ResponseEntity.ok(ApiResponse.success("获取默认配置成功", defaultConfig));
    }

    /**
     * 设置默认配置
     */
    @PostMapping("/{id}/set-default")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> setDefaultConfig(@PathVariable Long id) {

        aiConfigService.setDefaultConfig(id);

        return ResponseEntity.ok(ApiResponse.success("设置默认配置成功"));
    }

    /**
     * 启用/禁用配置
     */
    @PostMapping("/{id}/toggle")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<AiConfig>> toggleConfig(@PathVariable Long id) {

        AiConfig config = aiConfigService.toggleConfig(id);

        return ResponseEntity.ok(ApiResponse.success("配置状态切换成功", config));
    }

    /**
     * 测试配置连接
     */
    @PostMapping("/{id}/test")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<TestResult>> testConfig(@PathVariable Long id) {

        boolean success = aiConfigService.testConfig(id);

        TestResult result = new TestResult();
        result.setConfigId(id);
        result.setSuccess(success);
        result.setMessage(success ? "连接测试成功" : "连接测试失败");

        return ResponseEntity.ok(ApiResponse.success("测试完成", result));
    }

    /**
     * 从认证信息中获取用户ID
     */
    private Long getUserId(Authentication authentication) {
        try {
            String username = authentication.getName();
            return Long.parseLong(username);
        } catch (NumberFormatException e) {
            throw new RuntimeException("无法获取用户ID: " + authentication.getName());
        }
    }

    /**
     * 配置创建请求DTO
     */
    public static class ConfigCreateRequest {

        @NotNull(message = "AI提供商不能为空")
        private AiProvider provider;

        @NotBlank(message = "配置名称不能为空")
        @Size(max = 100, message = "配置名称不能超过100个字符")
        private String configName;

        @NotBlank(message = "API密钥不能为空")
        @Size(max = 500, message = "API密钥不能超过500个字符")
        private String apiKey;

        @Size(max = 255, message = "基础URL不能超过255个字符")
        private String baseUrl;

        @Size(max = 100, message = "模型名称不能超过100个字符")
        private String modelName;

        @Size(max = 4000, message = "系统提示词不能超过4000个字符")
        private String systemPrompt;

        private Double temperature = 0.7;

        private Integer maxTokens = 2000;

        private Boolean enabled = true;

        private Boolean isDefault = false;

        // Getters and Setters
        public AiProvider getProvider() {
            return provider;
        }

        public void setProvider(AiProvider provider) {
            this.provider = provider;
        }

        public String getConfigName() {
            return configName;
        }

        public void setConfigName(String configName) {
            this.configName = configName;
        }

        public String getApiKey() {
            return apiKey;
        }

        public void setApiKey(String apiKey) {
            this.apiKey = apiKey;
        }

        public String getBaseUrl() {
            return baseUrl;
        }

        public void setBaseUrl(String baseUrl) {
            this.baseUrl = baseUrl;
        }

        public String getModelName() {
            return modelName;
        }

        public void setModelName(String modelName) {
            this.modelName = modelName;
        }

        public String getSystemPrompt() {
            return systemPrompt;
        }

        public void setSystemPrompt(String systemPrompt) {
            this.systemPrompt = systemPrompt;
        }

        public Double getTemperature() {
            return temperature;
        }

        public void setTemperature(Double temperature) {
            this.temperature = temperature;
        }

        public Integer getMaxTokens() {
            return maxTokens;
        }

        public void setMaxTokens(Integer maxTokens) {
            this.maxTokens = maxTokens;
        }

        public Boolean getEnabled() {
            return enabled;
        }

        public void setEnabled(Boolean enabled) {
            this.enabled = enabled;
        }

        public Boolean getIsDefault() {
            return isDefault;
        }

        public void setIsDefault(Boolean isDefault) {
            this.isDefault = isDefault;
        }
    }

    /**
     * 配置更新请求DTO
     */
    public static class ConfigUpdateRequest {

        @Size(max = 100, message = "配置名称不能超过100个字符")
        private String configName;

        @Size(max = 500, message = "API密钥不能超过500个字符")
        private String apiKey;

        @Size(max = 255, message = "基础URL不能超过255个字符")
        private String baseUrl;

        @Size(max = 100, message = "模型名称不能超过100个字符")
        private String modelName;

        @Size(max = 4000, message = "系统提示词不能超过4000个字符")
        private String systemPrompt;

        private Double temperature;

        private Integer maxTokens;

        private Boolean enabled;

        private Boolean isDefault;

        // Getters and Setters
        public String getConfigName() {
            return configName;
        }

        public void setConfigName(String configName) {
            this.configName = configName;
        }

        public String getApiKey() {
            return apiKey;
        }

        public void setApiKey(String apiKey) {
            this.apiKey = apiKey;
        }

        public String getBaseUrl() {
            return baseUrl;
        }

        public void setBaseUrl(String baseUrl) {
            this.baseUrl = baseUrl;
        }

        public String getModelName() {
            return modelName;
        }

        public void setModelName(String modelName) {
            this.modelName = modelName;
        }

        public String getSystemPrompt() {
            return systemPrompt;
        }

        public void setSystemPrompt(String systemPrompt) {
            this.systemPrompt = systemPrompt;
        }

        public Double getTemperature() {
            return temperature;
        }

        public void setTemperature(Double temperature) {
            this.temperature = temperature;
        }

        public Integer getMaxTokens() {
            return maxTokens;
        }

        public void setMaxTokens(Integer maxTokens) {
            this.maxTokens = maxTokens;
        }

        public Boolean getEnabled() {
            return enabled;
        }

        public void setEnabled(Boolean enabled) {
            this.enabled = enabled;
        }

        public Boolean getIsDefault() {
            return isDefault;
        }

        public void setIsDefault(Boolean isDefault) {
            this.isDefault = isDefault;
        }
    }

    /**
     * 测试结果DTO
     */
    public static class TestResult {

        private Long configId;
        private boolean success;
        private String message;
        private long responseTime;

        // Getters and Setters
        public Long getConfigId() {
            return configId;
        }

        public void setConfigId(Long configId) {
            this.configId = configId;
        }

        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public long getResponseTime() {
            return responseTime;
        }

        public void setResponseTime(long responseTime) {
            this.responseTime = responseTime;
        }
    }
}
