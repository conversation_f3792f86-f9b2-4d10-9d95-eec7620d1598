import { ref, reactive, computed, onMounted, onBeforeUnmount } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useAiModelStore } from '@/store/aiModel';
import { useUserStore } from '@/store/user';
import axios from 'axios';

export default {
  setup() {
    const route = useRoute();
    const router = useRouter();
    const aiModelStore = useAiModelStore();
    const userStore = useUserStore();

    // 辩论数据
    const debate = reactive({
      id: route.params.id,
      title: '',
      category: '',
      status: 'waiting', // waiting, active, paused, completed
      host: {
        id: '',
        name: '',
        avatar: '',
        modelId: ''
      },
      supporter: {
        id: '',
        name: '',
        avatar: '',
        modelId: ''
      },
      opposer: {
        id: '',
        name: '',
        avatar: '',
        modelId: ''
      },
      judges: [],
      rounds: 5,
      roundDuration: 180 // 每轮持续时间（秒）
    });

    // 状态变量
    const speeches = ref([]); // 辩论发言历史
    const currentRound = ref(1);
    const currentSpeaker = ref(null); // 'host', 'supporter', 'opposer', 'judge-0', 'judge-1'...
    const isPlaying = ref(false);
    const timeRemaining = ref(0);
    const timer = ref(null);
    const loading = ref(false);
    const error = ref(null);

    // 计算属性
    const progress = computed(() => {
      return Math.round((currentRound.value / debate.rounds) * 100);
    });

    // 进度条样式
    const progressBarStyle = computed(() => ({
      '--progress-width': `${progress.value}%`
    }));

    // 用户权限
    const canControl = computed(() => {
      return userStore.isAdmin || userStore.user?.id === debate.creatorId;
    });

    const canNextRound = computed(() => {
      return isPlaying.value && currentRound.value < debate.rounds;
    });

    const canEnd = computed(() => {
      return debate.status === 'active';
    });

    // 方法
    // 获取辩论数据
    const fetchDebate = async () => {
      loading.value = true;
      error.value = null;

      try {
        // 从API获取辩论数据
        const response = await axios.get(`/api/ai-debates/${debate.id}`);

        // 更新本地状态
        Object.assign(debate, response.data);

        // 如果辩论已经开始，则设置相应的状态
        if (debate.status === 'active') {
          isPlaying.value = true;
          startTimer();
        }

        // 获取辩论历史
        await fetchSpeechHistory();
      } catch (err) {
        console.error('获取辩论数据失败:', err);
        error.value = '无法加载辩论数据，请稍后再试';
      } finally {
        loading.value = false;
      }
    };

    // 获取辩论历史记录
    const fetchSpeechHistory = async () => {
      try {
        const response = await axios.get(`/api/ai-debates/${debate.id}/speeches`);
        speeches.value = response.data;
      } catch (err) {
        console.error('获取辩论历史失败:', err);
      }
    };

    // 开始/暂停辩论
    const toggleDebate = async () => {
      if (!canControl.value) return;

      try {
        if (isPlaying.value) {
          // 暂停辩论
          await axios.post(`/api/ai-debates/${debate.id}/pause`);
          isPlaying.value = false;
          debate.status = 'paused';
          clearInterval(timer.value);
        } else {
          // 开始/继续辩论
          await axios.post(`/api/ai-debates/${debate.id}/start`);
          isPlaying.value = true;
          debate.status = 'active';
          startTimer();

          // 如果是刚开始辩论，由主持人发言
          if (!currentSpeaker.value) {
            currentSpeaker.value = 'host';
            generateAISpeech('host');
          }
        }
      } catch (err) {
        console.error('切换辩论状态失败:', err);
        error.value = '操作失败，请稍后再试';
      }
    };

    // 进入下一轮
    const nextRound = async () => {
      if (!canNextRound.value) return;

      try {
        await axios.post(`/api/ai-debates/${debate.id}/next-round`);
        currentRound.value++;
        resetTimer();
        currentSpeaker.value = 'host';
        generateAISpeech('host');
      } catch (err) {
        console.error('进入下一轮失败:', err);
        error.value = '操作失败，请稍后再试';
      }
    };

    // 结束辩论
    const endDebate = async () => {
      if (!canEnd.value) return;

      try {
        await axios.post(`/api/ai-debates/${debate.id}/end`);
        debate.status = 'completed';
        isPlaying.value = false;
        clearInterval(timer.value);

        // 由评委给出最终评判
        for (let i = 0; i < debate.judges.length; i++) {
          currentSpeaker.value = `judge-${i}`;
          await generateAISpeech(`judge-${i}`);
        }

        // 显示辩论结果
        router.push(`/ai-debate/${debate.id}/result`);
      } catch (err) {
        console.error('结束辩论失败:', err);
        error.value = '操作失败，请稍后再试';
      }
    };

    // 生成AI发言
    const generateAISpeech = async (speakerRole) => {
      let speakerId;
      let modelId;
      let speakerName;
      let side;

      // 确定发言者信息
      if (speakerRole === 'host') {
        speakerId = debate.host.id;
        modelId = debate.host.modelId;
        speakerName = debate.host.name;
        side = 'host';
      } else if (speakerRole === 'supporter') {
        speakerId = debate.supporter.id;
        modelId = debate.supporter.modelId;
        speakerName = debate.supporter.name;
        side = 'supporter';
      } else if (speakerRole === 'opposer') {
        speakerId = debate.opposer.id;
        modelId = debate.opposer.modelId;
        speakerName = debate.opposer.name;
        side = 'opposer';
      } else if (speakerRole.startsWith('judge-')) {
        const index = parseInt(speakerRole.split('-')[1]);
        if (index >= 0 && index < debate.judges.length) {
          speakerId = debate.judges[index].id;
          modelId = debate.judges[index].modelId;
          speakerName = debate.judges[index].name;
          side = 'judge';
        }
      }

      if (!speakerId || !modelId) {
        console.error('无效的发言者角色', speakerRole);
        return;
      }

      try {
        // 获取历史发言作为上下文
        const context = speeches.value.map(s => ({
          role: s.role,
          content: s.content,
          speaker: s.speaker
        }));

        // 向API发送请求生成AI发言
        const response = await axios.post('/api/ai/generate-speech', {
          debateId: debate.id,
          modelId: modelId,
          speakerId: speakerId,
          speakerRole: speakerRole,
          round: currentRound.value,
          context: context
        });

        // 添加新发言到历史记录
        const newSpeech = {
          id: response.data.id,
          debateId: debate.id,
          speaker: speakerName,
          role: speakerRole,
          side: side,
          content: response.data.content,
          timestamp: new Date(),
          round: currentRound.value
        };

        speeches.value.push(newSpeech);

        // 发送到后端保存
        await axios.post(`/api/ai-debates/${debate.id}/speeches`, newSpeech);

        // 确定下一个发言者
        determineNextSpeaker();
      } catch (err) {
        console.error('生成AI发言失败:', err);
        error.value = 'AI生成回复失败，请稍后再试';
      }
    };

    // 确定下一个发言者
    const determineNextSpeaker = () => {
      // 简单的轮换逻辑
      if (currentSpeaker.value === 'host') {
        currentSpeaker.value = 'supporter';
        setTimeout(() => generateAISpeech('supporter'), 2000);
      } else if (currentSpeaker.value === 'supporter') {
        currentSpeaker.value = 'opposer';
        setTimeout(() => generateAISpeech('opposer'), 2000);
      } else if (currentSpeaker.value === 'opposer') {
        if (currentRound.value === debate.rounds) {
          // 如果是最后一轮，由评委发言
          if (debate.judges.length > 0) {
            currentSpeaker.value = 'judge-0';
            setTimeout(() => generateAISpeech('judge-0'), 2000);
          } else {
            // 没有评委，辩论结束
            endDebate();
          }
        } else {
          // 下一轮由主持人开始
          currentSpeaker.value = 'host';
          nextRound();
        }
      } else if (currentSpeaker.value.startsWith('judge-')) {
        const index = parseInt(currentSpeaker.value.split('-')[1]);
        if (index + 1 < debate.judges.length) {
          // 下一个评委发言
          currentSpeaker.value = `judge-${index + 1}`;
          setTimeout(() => generateAISpeech(`judge-${index + 1}`), 2000);
        } else {
          // 所有评委都发言完毕
          currentSpeaker.value = null;
        }
      }
    };

    // 计时器相关方法
    const startTimer = () => {
      if (timer.value) clearInterval(timer.value);

      timeRemaining.value = debate.roundDuration;

      timer.value = setInterval(() => {
        if (timeRemaining.value > 0) {
          timeRemaining.value--;
        } else {
          // 时间到，执行下一轮逻辑
          clearInterval(timer.value);
          if (currentRound.value < debate.rounds) {
            nextRound();
          } else {
            endDebate();
          }
        }
      }, 1000);
    };

    const resetTimer = () => {
      clearInterval(timer.value);
      timeRemaining.value = debate.roundDuration;
      startTimer();
    };

    // 格式化时间显示
    const formatTime = (timestamp) => {
      if (!timestamp) return '';

      const date = new Date(timestamp);
      return date.toLocaleString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    };

    // 格式化持续时间
    const formatDuration = (seconds) => {
      const mins = Math.floor(seconds / 60);
      const secs = seconds % 60;
      return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    };

    // 获取状态文本
    const getStatusText = (status) => {
      const statusMap = {
        'waiting': '等待中',
        'active': '进行中',
        'paused': '已暂停',
        'completed': '已结束'
      };
      return statusMap[status] || '未知状态';
    };

    // 生命周期钩子
    onMounted(async () => {
      await fetchDebate();
    });

    onBeforeUnmount(() => {
      if (timer.value) clearInterval(timer.value);
    });

    return {
      debate,
      speeches,
      currentRound,
      currentSpeaker,
      isPlaying,
      timeRemaining,
      progress,
      progressBarStyle,
      loading,
      error,
      canControl,
      canNextRound,
      canEnd,
      toggleDebate,
      nextRound,
      endDebate,
      formatTime,
      formatDuration,
      getStatusText
    };
  }
};
