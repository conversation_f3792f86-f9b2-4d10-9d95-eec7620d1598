# AI辩论赛平台 - 前端动态端口配置说明

## 🎯 功能概述

本配置实现了前端自动检测后端服务端口的功能，无需手动修改配置文件，前端会自动适应后端的端口变化。

## 🔧 实现原理

### 1. 端口检测机制
- 自动检测常用端口：8081 → 8080 → 8082 → 8083 → 8084 → 8085
- 验证Spring Boot健康检查端点：`/api/actuator/health`
- 确保检测到的是真正的后端服务

### 2. 代理转发机制
- 前端使用Vite代理功能
- 所有`/api`请求自动转发到检测到的后端端口
- 开发环境下前端URL为相对路径，避免硬编码端口

### 3. 配置文件结构
```
debate-tournament-frontend/
├── vite.config.js          # Vite配置，包含动态代理
├── .env.development        # 开发环境配置（使用代理）
├── scripts/
│   ├── detect-backend.js   # 后端端口检测脚本
│   └── start-dev.js        # 智能启动脚本
└── package.json            # 新增启动命令
```

## 🚀 使用方法

### 方法一：使用PowerShell脚本（推荐）
```powershell
# 在项目根目录执行
.\start-frontend.ps1
```

### 方法二：使用批处理文件
```cmd
# 在项目根目录执行
start-frontend.bat
```

### 方法三：使用npm命令
```bash
# 进入前端目录
cd debate-tournament-frontend

# 自动检测并启动
npm run dev:auto

# 或者手动检测端口
npm run detect-backend

# 然后启动前端
npm run dev
```

### 方法四：传统方式
```bash
# 进入前端目录
cd debate-tournament-frontend

# 直接启动（使用默认端口8081）
npm run dev
```

## ⚙️ 配置说明

### 环境变量
- `DETECTED_BACKEND_PORT`: 检测到的后端端口号
- `VITE_API_BASE_URL`: API基础URL（开发环境使用`/api`）

### Vite代理配置
```javascript
server: {
  proxy: {
    '/api': {
      target: `http://localhost:${backendPort}`,
      changeOrigin: true,
      secure: false
    }
  }
}
```

### 端口检测顺序
1. **8081** - 默认后端端口
2. **8080** - 备用端口
3. **8082** - 备用端口
4. **8083** - 备用端口
5. **8084** - 备用端口
6. **8085** - 备用端口

## 🔍 故障排除

### 问题1：检测不到后端服务
**原因**: 后端服务未启动或端口不在检测范围内
**解决**: 
1. 确保后端服务已启动
2. 检查后端端口是否在检测范围内
3. 手动指定端口：`set DETECTED_BACKEND_PORT=端口号`

### 问题2：代理请求失败
**原因**: CORS配置或网络问题
**解决**:
1. 检查后端CORS配置是否包含前端端口
2. 确认后端健康检查端点可访问
3. 查看浏览器控制台错误信息

### 问题3：前端启动失败
**原因**: 依赖未安装或Node.js版本问题
**解决**:
1. 运行 `npm install` 安装依赖
2. 检查Node.js版本是否兼容
3. 清除缓存：`npm cache clean --force`

## 📝 开发说明

### 修改检测端口范围
编辑 `scripts/detect-backend.js` 文件：
```javascript
const candidatePorts = [8081, 8080, 8082, 8083, 8084, 8085];
```

### 修改代理配置
编辑 `vite.config.js` 文件的proxy部分。

### 添加新的启动脚本
在 `package.json` 的scripts部分添加新命令。

## 🎉 优势特点

1. **自动化**: 无需手动修改配置文件
2. **智能检测**: 自动识别真正的后端服务
3. **容错性强**: 多端口检测，降级到默认端口
4. **开发友好**: 提供多种启动方式
5. **跨平台**: 支持Windows PowerShell和批处理

## 📋 注意事项

1. 确保后端服务的健康检查端点 `/api/actuator/health` 可访问
2. 开发环境下前端使用代理，生产环境需要相应配置
3. 如果后端端口频繁变化，建议固定后端端口配置
4. 首次运行可能需要安装前端依赖

---

**提示**: 如果遇到问题，请检查控制台输出的详细日志信息。
