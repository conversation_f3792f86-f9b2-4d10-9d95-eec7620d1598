<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户状态调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            margin: 5px;
            padding: 8px 15px;
            cursor: pointer;
        }
        pre {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>用户状态调试工具</h1>
    
    <div class="section">
        <h2>当前localStorage状态</h2>
        <button onclick="showLocalStorage()">显示localStorage</button>
        <button onclick="clearLocalStorage()">清除localStorage</button>
        <pre id="localStorage-content"></pre>
    </div>
    
    <div class="section">
        <h2>测试登录API</h2>
        <button onclick="testCaptcha()">获取验证码</button>
        <button onclick="testLogin()">测试登录</button>
        <pre id="api-result"></pre>
    </div>
    
    <div class="section">
        <h2>操作</h2>
        <button onclick="simulateLogin()">模拟登录成功</button>
        <button onclick="simulateLogout()">模拟登出</button>
    </div>

    <script>
        function showLocalStorage() {
            const storage = {};
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                storage[key] = localStorage.getItem(key);
            }
            document.getElementById('localStorage-content').textContent = JSON.stringify(storage, null, 2);
        }
        
        function clearLocalStorage() {
            localStorage.clear();
            showLocalStorage();
            alert('localStorage已清除');
        }
        
        async function testCaptcha() {
            try {
                const response = await fetch('/api/auth/captcha/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                const result = await response.json();
                document.getElementById('api-result').textContent = JSON.stringify(result, null, 2);
            } catch (error) {
                document.getElementById('api-result').textContent = '错误: ' + error.message;
            }
        }
        
        async function testLogin() {
            try {
                const loginData = {
                    username: 'BaSui',
                    password: '123456',
                    captcha: 'TEST',
                    sessionId: 'test-session'
                };
                
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(loginData)
                });
                const result = await response.json();
                document.getElementById('api-result').textContent = JSON.stringify(result, null, 2);
            } catch (error) {
                document.getElementById('api-result').textContent = '错误: ' + error.message;
            }
        }
        
        function simulateLogin() {
            const mockUserInfo = {
                id: 6,
                username: 'BaSui',
                email: '<EMAIL>',
                avatar: ''
            };
            const mockToken = 'mock-jwt-token-12345';
            
            localStorage.setItem('token', mockToken);
            localStorage.setItem('userInfo', JSON.stringify(mockUserInfo));
            
            showLocalStorage();
            alert('模拟登录成功');
        }
        
        function simulateLogout() {
            localStorage.removeItem('token');
            localStorage.removeItem('userInfo');
            localStorage.removeItem('rememberMe');
            localStorage.removeItem('username');
            
            showLocalStorage();
            alert('模拟登出成功');
        }
        
        // 页面加载时显示当前状态
        window.onload = function() {
            showLocalStorage();
        };
    </script>
</body>
</html>
