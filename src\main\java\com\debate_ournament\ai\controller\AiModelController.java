package com.debate_ournament.ai.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.debate_ournament.ai.entity.AiConfig;
import com.debate_ournament.ai.entity.AiProvider;
import com.debate_ournament.ai.repository.AiConfigRepository;
import com.debate_ournament.ai.service.AiChatService;
import com.debate_ournament.ai.service.AiClient;
import com.debate_ournament.ai.service.AiClientFactory;
import com.debate_ournament.dto.common.ApiResponse;
import com.debate_ournament.users.entity.User;
import com.debate_ournament.users.service.UserService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * AI模型控制器
 * 提供AI模型相关的REST API接口
 */
@RestController
@RequestMapping("/api")
@Tag(name = "AI模型", description = "AI模型管理接口")
public class AiModelController {

    @Autowired
    private AiChatService aiChatService;

    @Autowired
    private AiConfigRepository aiConfigRepository;

    @Autowired
    private AiClientFactory aiClientFactory;

    @Autowired
    private UserService userService;

    /**
     * 获取可用的AI模型列表
     */
    @GetMapping("/ai-models")
    @Operation(summary = "获取AI模型列表", description = "获取系统中可用的AI模型列表")
    public ResponseEntity<ApiResponse<List<Map<String, Object>>>> getAvailableModels() {

        List<Map<String, Object>> models = new ArrayList<>();

        // 获取所有支持的AI提供商
        List<AiProvider> providers = aiChatService.getSupportedProviders();

        for (AiProvider provider : providers) {
            // 获取每个提供商支持的模型
            List<String> supportedModels = getSupportedModelsForProvider(provider);

            for (String modelName : supportedModels) {
                Map<String, Object> model = new HashMap<>();
                model.put("id", provider.getCode() + "-" + modelName);
                model.put("name", modelName);
                model.put("displayName", getDisplayName(provider, modelName));
                model.put("provider", provider.getCode());
                model.put("providerName", provider.getName());
                model.put("enabled", true);
                model.put("type", "chat");

                models.add(model);
            }
        }

        return ResponseEntity.ok(ApiResponse.success("获取AI模型列表成功", models));
    }

    /**
     * 获取用户自定义AI模型配置
     */
    @GetMapping("/user/ai-models")
    @PreAuthorize("hasRole('USER')")
    @Operation(summary = "获取用户AI模型配置", description = "获取当前用户的自定义AI模型配置")
    public ResponseEntity<ApiResponse<List<Map<String, Object>>>> getUserModels(Authentication authentication) {

        Long userId = getUserId(authentication);
        List<Map<String, Object>> userModels = new ArrayList<>();

        try {
            // 获取用户的AI配置
            List<AiConfig> userConfigs = aiConfigRepository.findByCreatedByAndEnabledTrueOrderByCreatedAtDesc(userId);

            for (AiConfig config : userConfigs) {
                Map<String, Object> model = new HashMap<>();
                model.put("id", "user-" + config.getId());
                model.put("configId", config.getId());
                model.put("name", config.getConfigName());
                model.put("displayName", config.getConfigName());
                model.put("provider", config.getProvider().getCode());
                model.put("providerName", config.getProvider().getName());
                model.put("modelName", config.getModelName());
                model.put("enabled", config.getEnabled());
                model.put("isDefault", config.getIsDefault());
                model.put("type", "custom");

                userModels.add(model);
            }
        } catch (Exception e) {
            // 如果获取失败，返回空列表
            userModels = new ArrayList<>();
        }

        return ResponseEntity.ok(ApiResponse.success("获取用户AI模型配置成功", userModels));
    }

    /**
     * 获取指定提供商的模型列表
     */
    @GetMapping("/ai-models/provider/{provider}")
    @Operation(summary = "获取指定提供商的模型列表", description = "获取指定AI提供商支持的模型列表")
    public ResponseEntity<ApiResponse<List<String>>> getProviderModels(@PathVariable String provider) {

        try {
            AiProvider aiProvider = AiProvider.valueOf(provider.toUpperCase());
            List<String> models = aiChatService.getSupportedModels(aiProvider, null);

            return ResponseEntity.ok(ApiResponse.success("获取模型列表成功", models));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("不支持的AI提供商: " + provider));
        }
    }

    /**
     * 获取指定提供商支持的模型列表（不需要配置）
     */
    private List<String> getSupportedModelsForProvider(AiProvider provider) {
        try {
            AiClient aiClient = aiClientFactory.getClient(provider);
            // 创建一个临时配置对象，只是为了调用getSupportedModels方法
            AiConfig tempConfig = new AiConfig();
            tempConfig.setProvider(provider);
            return aiClient.getSupportedModels(tempConfig);
        } catch (Exception e) {
            // 如果获取失败，返回空列表
            return List.of();
        }
    }

    /**
     * 获取用户ID
     */
    private Long getUserId(Authentication authentication) {
        if (authentication == null || authentication.getName() == null) {
            throw new RuntimeException("用户未登录");
        }

        User user = userService.findByUsername(authentication.getName())
            .orElseThrow(() -> new RuntimeException("用户不存在"));

        return user.getId();
    }

    /**
     * 获取模型显示名称
     */
    private String getDisplayName(AiProvider provider, String modelName) {
        // 根据提供商和模型名称生成友好的显示名称
        return switch (provider) {
            case OPENAI -> {
                if (modelName.contains("gpt-4o")) yield "GPT-4o";
                if (modelName.contains("gpt-4")) yield "GPT-4";
                if (modelName.contains("gpt-3.5")) yield "GPT-3.5 Turbo";
                yield modelName;
            }
            case ANTHROPIC -> {
                if (modelName.contains("claude-3-sonnet")) yield "Claude 3 Sonnet";
                if (modelName.contains("claude-3-haiku")) yield "Claude 3 Haiku";
                yield modelName;
            }
            case DEEPSEEK -> {
                if (modelName.contains("deepseek-chat")) yield "DeepSeek Chat";
                if (modelName.contains("deepseek-coder")) yield "DeepSeek Coder";
                yield modelName;
            }
            case ALIBABA_BAILIAN -> {
                if (modelName.contains("qwen-max")) yield "通义千问 Max";
                if (modelName.contains("qwen-plus")) yield "通义千问 Plus";
                if (modelName.contains("qwen-turbo")) yield "通义千问 Turbo";
                yield modelName;
            }
            case ALIBABA -> {
                if (modelName.contains("qwen-max")) yield "通义千问 Max";
                if (modelName.contains("qwen-plus")) yield "通义千问 Plus";
                if (modelName.contains("qwen-turbo")) yield "通义千问 Turbo";
                yield modelName;
            }
            case SILICONFLOW -> {
                if (modelName.contains("Qwen2-72B")) yield "通义千问2-72B";
                if (modelName.contains("glm-4")) yield "智谱GLM-4";
                if (modelName.contains("Meta-Llama-3")) yield "Meta Llama 3";
                yield modelName;
            }
            case BAIDU -> {
                if (modelName.contains("ernie-bot-4")) yield "文心一言 4.0";
                if (modelName.contains("ernie-bot-turbo")) yield "文心一言 Turbo";
                if (modelName.contains("ernie-bot")) yield "文心一言";
                yield modelName;
            }
            case ZHIPU -> {
                if (modelName.contains("glm-4")) yield "智谱GLM-4";
                if (modelName.contains("glm-3")) yield "智谱GLM-3";
                yield modelName;
            }
            case TENCENT -> {
                if (modelName.contains("hunyuan-pro")) yield "腾讯混元 Pro";
                if (modelName.contains("hunyuan-standard")) yield "腾讯混元 Standard";
                if (modelName.contains("hunyuan")) yield "腾讯混元";
                yield modelName;
            }
            case MOONSHOT -> {
                if (modelName.contains("moonshot-v1-128k")) yield "Kimi 128K";
                if (modelName.contains("moonshot-v1-32k")) yield "Kimi 32K";
                if (modelName.contains("moonshot-v1-8k")) yield "Kimi 8K";
                yield modelName;
            }
            case OPENROUTER -> {
                if (modelName.contains("gpt-4")) yield "GPT-4 (OpenRouter)";
                if (modelName.contains("claude")) yield "Claude (OpenRouter)";
                if (modelName.contains("llama")) yield "Llama (OpenRouter)";
                yield modelName;
            }
        };
    }
}
