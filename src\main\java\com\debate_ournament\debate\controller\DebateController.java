package com.debate_ournament.debate.controller;

import com.debate_ournament.dto.common.ApiResponse;
import com.debate_ournament.debate.entity.Debate;
import com.debate_ournament.debate.service.DebateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.Optional;

/**
 * 辩论控制器
 * 提供辩论相关的REST API接口
 */
@RestController
@RequestMapping("/api/debates")
@CrossOrigin(origins = "*")
public class DebateController {

    @Autowired
    private DebateService debateService;

    /**
     * 获取辩论列表
     */
    @GetMapping
    public ResponseEntity<ApiResponse<Page<Debate>>> getDebates(
            @RequestParam(defaultValue = "") String category,
            @RequestParam(defaultValue = "") String status,
            @RequestParam(defaultValue = "") String search,
            @RequestParam(defaultValue = "createdAt") String sort,
            @RequestParam(defaultValue = "desc") String direction,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int limit) {

        try {
            // 转换页码（前端从1开始，Spring Data从0开始）
            int pageNumber = Math.max(0, page - 1);
            
            Sort.Direction sortDirection = "desc".equalsIgnoreCase(direction) ?
                Sort.Direction.DESC : Sort.Direction.ASC;
            Pageable pageable = PageRequest.of(pageNumber, limit, Sort.by(sortDirection, sort));

            Page<Debate> debates = debateService.getDebates(category, status, search, pageable);

            return ResponseEntity.ok(ApiResponse.success("获取辩论列表成功", debates));

        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                .body(ApiResponse.error("获取辩论列表失败: " + e.getMessage()));
        }
    }

    /**
     * 获取辩论详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<Debate>> getDebateById(@PathVariable Long id) {
        try {
            Optional<Debate> debate = debateService.getDebateById(id);
            
            if (debate.isPresent()) {
                return ResponseEntity.ok(ApiResponse.success("获取辩论详情成功", debate.get()));
            } else {
                return ResponseEntity.notFound().build();
            }

        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                .body(ApiResponse.error("获取辩论详情失败: " + e.getMessage()));
        }
    }

    /**
     * 创建辩论
     */
    @PostMapping
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Debate>> createDebate(
            @RequestBody Map<String, Object> debateData,
            Authentication authentication) {

        try {
            String username = authentication.getName();
            Debate debate = debateService.createDebate(debateData, username);

            return ResponseEntity.ok(ApiResponse.success("创建辩论成功", debate));

        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("创建辩论失败: " + e.getMessage()));
        }
    }

    /**
     * 更新辩论
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Debate>> updateDebate(
            @PathVariable Long id,
            @RequestBody Map<String, Object> debateData,
            Authentication authentication) {

        try {
            String username = authentication.getName();
            Debate debate = debateService.updateDebate(id, debateData, username);

            return ResponseEntity.ok(ApiResponse.success("更新辩论成功", debate));

        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("更新辩论失败: " + e.getMessage()));
        }
    }

    /**
     * 删除辩论
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<String>> deleteDebate(
            @PathVariable Long id,
            Authentication authentication) {

        try {
            String username = authentication.getName();
            debateService.deleteDebate(id, username);

            return ResponseEntity.ok(ApiResponse.success("删除辩论成功", "辩论已删除"));

        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("删除辩论失败: " + e.getMessage()));
        }
    }

    /**
     * 加入辩论
     */
    @PostMapping("/{id}/join")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<String>> joinDebate(
            @PathVariable Long id,
            @RequestBody Map<String, String> joinData,
            Authentication authentication) {

        try {
            String username = authentication.getName();
            String side = joinData.get("side");
            
            debateService.joinDebate(id, username, side);

            return ResponseEntity.ok(ApiResponse.success("加入辩论成功", "已成功加入辩论"));

        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("加入辩论失败: " + e.getMessage()));
        }
    }

    /**
     * 获取实时辩论列表
     */
    @GetMapping("/live")
    public ResponseEntity<ApiResponse<Page<Debate>>> getLiveDebates(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
            Page<Debate> liveDebates = debateService.getLiveDebates(pageable);

            return ResponseEntity.ok(ApiResponse.success("获取实时辩论列表成功", liveDebates));

        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                .body(ApiResponse.error("获取实时辩论列表失败: " + e.getMessage()));
        }
    }

    /**
     * 获取热门辩论列表
     */
    @GetMapping("/popular")
    public ResponseEntity<ApiResponse<Page<Debate>>> getPopularDebates(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<Debate> popularDebates = debateService.getPopularDebates(pageable);

            return ResponseEntity.ok(ApiResponse.success("获取热门辩论列表成功", popularDebates));

        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                .body(ApiResponse.error("获取热门辩论列表失败: " + e.getMessage()));
        }
    }

    /**
     * 获取用户参与的辩论
     */
    @GetMapping("/my")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Page<Debate>>> getMyDebates(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            Authentication authentication) {

        try {
            String username = authentication.getName();
            Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
            Page<Debate> myDebates = debateService.getUserDebates(username, pageable);

            return ResponseEntity.ok(ApiResponse.success("获取我的辩论列表成功", myDebates));

        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                .body(ApiResponse.error("获取我的辩论列表失败: " + e.getMessage()));
        }
    }
}
