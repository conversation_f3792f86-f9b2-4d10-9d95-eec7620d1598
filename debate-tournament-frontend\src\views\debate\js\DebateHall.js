import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useDebateStore } from '@/store/debate';
import { useUserStore } from '@/store/user';
import DebateCard from '@/components/debate/DebateCard.vue';

export default {
  components: {
    DebateCard
  },

  setup() {
    const router = useRouter();
    const debateStore = useDebateStore();
    const userStore = useUserStore();

    // 数据
    const loading = ref(false);
    const createLoading = ref(false);
    const createModalOpen = ref(false);
    const currentPage = ref(1);
    const itemsPerPage = 12;

    const filters = reactive({
      category: '',
      status: '',
      search: ''
    });

    const newDebate = reactive({
      title: '',
      description: '',
      category: '',
      customizeAI: false
    });

    const categories = ref([
      { id: 'politics', name: '政治' },
      { id: 'economics', name: '经济' },
      { id: 'culture', name: '文化' },
      { id: 'technology', name: '科技' },
      { id: 'education', name: '教育' },
      { id: 'environment', name: '环境' },
      { id: 'ethics', name: '伦理' },
      { id: 'social', name: '社会' }
    ]);

    // 计算属性
    const filteredDebates = computed(() => {
      let result = [...debateStore.debates];

      // 按分类筛选
      if (filters.category) {
        result = result.filter(debate => debate.category === filters.category);
      }

      // 按状态筛选
      if (filters.status) {
        result = result.filter(debate => debate.status === filters.status);
      }

      // 按关键词搜索
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        result = result.filter(debate =>
          debate.title.toLowerCase().includes(searchLower) ||
          debate.description.toLowerCase().includes(searchLower)
        );
      }

      return result;
    });

    const totalPages = computed(() => {
      return Math.ceil(filteredDebates.value.length / itemsPerPage);
    });

    const pageNumbers = computed(() => {
      const pages = [];
      for (let i = 1; i <= totalPages.value; i++) {
        pages.push(i);
      }
      return pages;
    });

    const paginatedDebates = computed(() => {
      const startIndex = (currentPage.value - 1) * itemsPerPage;
      const endIndex = startIndex + itemsPerPage;
      return filteredDebates.value.slice(startIndex, endIndex);
    });

    // 方法
    const fetchDebates = async () => {
      loading.value = true;
      try {
        await debateStore.fetchDebates();
      } catch (error) {
        console.error('Error fetching debates:', error);
      } finally {
        loading.value = false;
      }
    };

    const handleSearchInput = () => {
      currentPage.value = 1;
    };

    const setTopicFilter = (categoryId) => {
      if (filters.category === categoryId) {
        filters.category = ''; // 取消选择
      } else {
        filters.category = categoryId; // 选择此分类
      }
      currentPage.value = 1;
    };

    const changePage = (page) => {
      currentPage.value = page;
      window.scrollTo(0, 0);
    };

    const navigateToDebate = (debateId) => {
      router.push(`/debate-arena/${debateId}`);
    };

    const openCreateModal = () => {
      if (!userStore.isLoggedIn) {
        router.push('/login');
        return;
      }
      createModalOpen.value = true;
    };

    const handleCreateDebate = async () => {
      // 验证表单
      if (!newDebate.title || !newDebate.description || !newDebate.category) {
        return;
      }

      createLoading.value = true;
      try {
        const debateId = await debateStore.createDebate({
          ...newDebate,
          creator: userStore.user.id,
          status: 'waiting'
        });

        createModalOpen.value = false;

        // 重置表单
        Object.assign(newDebate, {
          title: '',
          description: '',
          category: '',
          customizeAI: false
        });

        // 跳转到新创建的辩论
        router.push(`/debate-arena/${debateId}`);
      } catch (error) {
        console.error('Error creating debate:', error);
      } finally {
        createLoading.value = false;
      }
    };

    // 生命周期钩子
    onMounted(() => {
      fetchDebates();
    });

    return {
      loading,
      createLoading,
      createModalOpen,
      currentPage,
      filters,
      newDebate,
      categories,
      filteredDebates,
      totalPages,
      pageNumbers,
      paginatedDebates,
      handleSearchInput,
      setTopicFilter,
      changePage,
      navigateToDebate,
      openCreateModal,
      handleCreateDebate
    };
  }
};
