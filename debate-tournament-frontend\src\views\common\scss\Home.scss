@use '@/styles/variables' as v;
@use '@/styles/mixins' as m;

// 全局动画
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}

// 骨架屏动画
.skeleton-loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 8px;
}

// 页面入场动画
.home {
  animation: fadeInUp 0.8s ease-out;
}

// 英雄区域
.hero-section {
  position: relative;
  padding: 96px 0 64px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  overflow: hidden;
  min-height: 480px;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('@/assets/images/pattern.svg');
    opacity: 0.08;
    z-index: 0;
    animation: shimmer 20s infinite;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 50%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 80%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
    z-index: 1;
  }

  .container {
    position: relative;
    z-index: 2;
  }

  .hero-content {
    display: flex;
    align-items: center;
    gap: 56px;

    @include m.respond-to(md) {
      flex-direction: column-reverse;
      gap: 32px;
    }

    &__text {
      flex: 1;
      animation: fadeInLeft 1s ease-out;

      .hero-content__title {
        font-size: 3.2rem;
        font-weight: 800;
        margin-bottom: 1.2rem;
        letter-spacing: 2px;
        text-shadow: 0 4px 24px rgba(80, 60, 160, 0.12);
        background: linear-gradient(45deg, #fff, #e8e8ff);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;

        @include m.respond-to(md) {
          font-size: 2.4rem;
        }
      }

      .hero-content__subtitle {
        font-size: 1.3rem;
        margin-bottom: 2.2rem;
        opacity: 0.92;
        font-weight: 400;
        animation: fadeInUp 1s ease-out 0.3s both;
      }

      .hero-content__features {
        display: flex;
        flex-wrap: wrap;
        gap: 18px 32px;
        margin-bottom: 2.2rem;
        animation: fadeInUp 1s ease-out 0.6s both;

        .feature-item {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 1.08rem;
          padding: 8px 16px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 25px;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.2);
          transition: all 0.3s ease;

          &:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
          }

          .el-icon {
            font-size: 22px;
            color: #fff;
            filter: drop-shadow(0 2px 8px #764ba2cc);
          }
        }
      }

      .hero-content__actions {
        display: flex;
        gap: 18px;
        animation: fadeInUp 1s ease-out 0.9s both;

        @include m.respond-to(sm) {
          flex-direction: column;
          align-items: center;
        }

        .el-button {
          font-size: 1.1rem;
          padding: 0 2.2em;
          border-radius: 24px;
          font-weight: 600;
          box-shadow: 0 2px 16px rgba(102, 126, 234, 0.08);
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
          }

          &:hover::before {
            left: 100%;
          }

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
          }
        }
      }
    }

    &__visual {
      flex: 1;
      min-width: 320px;
      animation: fadeInRight 1s ease-out 0.3s both;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 200px;
        height: 200px;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        animation: pulse 4s ease-in-out infinite;
      }
    }
  }
}

// 通用标题
.section-title {
  text-align: center;
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 2.5rem;
  color: #3a3a4a;
  letter-spacing: 1px;

  &::after {
    content: '';
    display: block;
    width: 64px;
    height: 3px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    margin: 0.7rem auto 0;
    border-radius: 2px;
  }
}

.section-action {
  margin-top: 2.5rem;
  text-align: center;
}

// 功能卡片区
.features-section {
  padding: 72px 0 56px 0;
  background: linear-gradient(135deg, #f7f8fa 0%, #e8eaf6 100%);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(102, 126, 234, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 70% 80%, rgba(118, 75, 162, 0.05) 0%, transparent 50%);
    pointer-events: none;
  }
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 32px;
  position: relative;
  z-index: 1;
}

.feature-card {
  background: #fff;
  border-radius: 20px;
  padding: 36px 28px 32px 28px;
  box-shadow: 0 4px 24px rgba(102, 126, 234, 0.08);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: center;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(102, 126, 234, 0.1);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.4s ease;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
  }

  &:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow: 0 20px 40px rgba(102, 126, 234, 0.2);

    &::before {
      transform: scaleX(1);
    }

    &::after {
      opacity: 1;
    }

    .feature-card__icon .el-icon {
      transform: scale(1.1) rotate(5deg);
      color: #5a67d8;
    }

    .feature-card__title {
      color: #667eea;
    }
  }

  &__icon {
    margin-bottom: 1.2rem;
    position: relative;
    z-index: 2;

    .el-icon {
      font-size: 48px;
      color: #667eea;
      filter: drop-shadow(0 2px 12px #667eea33);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }
  }

  &__title {
    font-size: 1.18rem;
    font-weight: 700;
    margin-bottom: 1.1rem;
    color: #3a3a4a;
    transition: color 0.3s ease;
    position: relative;
    z-index: 2;
  }

  &__list {
    text-align: left;
    margin-bottom: 1.5rem;
    padding-left: 18px;
    position: relative;
    z-index: 2;

    li {
      margin-bottom: 8px;
      color: #7b7b8b;
      font-size: 1.01rem;
      position: relative;
      padding-left: 16px;
      transition: color 0.3s ease;

      &::before {
        content: '✨';
        position: absolute;
        left: 0;
        top: 0;
        font-size: 12px;
        opacity: 0.7;
      }

      &:last-child {
        margin-bottom: 0;
      }

      &:hover {
        color: #5a5a6a;
      }
    }
  }

  .el-button {
    border-radius: 25px;
    font-weight: 600;
    font-size: 1.05rem;
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
      transition: left 0.5s;
    }

    &:hover::before {
      left: 100%;
    }
  }

  // 每个卡片的入场动画延迟
  &:nth-child(1) {
    animation: fadeInUp 0.8s ease-out 0.2s both;
  }

  &:nth-child(2) {
    animation: fadeInUp 0.8s ease-out 0.4s both;
  }

  &:nth-child(3) {
    animation: fadeInUp 0.8s ease-out 0.6s both;
  }

  &:nth-child(4) {
    animation: fadeInUp 0.8s ease-out 0.8s both;
  }
}

// 实时辩论区
.live-debates-section {
  padding: 72px 0 56px 0;
  background: #fff;
}

.live-debates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(340px, 1fr));
  gap: 32px;
}

// AI模型区
.ai-models-section {
  padding: 72px 0 56px 0;
  background: #f5f7fa;
}

.models-tabs {
  max-width: 1100px;
  margin: 0 auto;
}

.models-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  gap: 32px;
  margin-top: 2rem;
}

.model-card {
  background: #fff;
  border-radius: 16px;
  padding: 32px 20px 28px 20px;
  box-shadow: 0 4px 18px rgba(102, 126, 234, 0.07);
  transition: all 0.22s;
  text-align: center;

  &:hover {
    transform: translateY(-6px) scale(1.02);
    box-shadow: 0 10px 28px rgba(102, 126, 234, 0.13);
  }

  &__name {
    font-size: 1.13rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #3a3a4a;
  }

  &__features {
    margin-bottom: 1.3rem;
  }

  &--custom {
    border: 2px dashed #ddd;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
}

.model-feature {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 8px;

  .el-icon {
    color: #667eea;
  }
}

// 思维导图区
.mind-map-section {
  padding: 72px 0 56px 0;
  background: #fff;
}

.mind-map-demo {
  max-width: 1100px;
  margin: 0 auto;
}

.mind-map-controls {
  margin-top: 1.5rem;
  text-align: center;
}

// 社区互动区
.community-section {
  padding: 72px 0 56px 0;
  background: #f5f7fa;
}

.community-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(340px, 1fr));
  gap: 32px;
  max-width: 1100px;
  margin: 0 auto;
}

.community-card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 18px rgba(102, 126, 234, 0.07);
  overflow: hidden;

  &__header {
    padding: 1.2rem 1.5rem;
    background: #f8f9fa;
    border-bottom: 1px solid #eee;
  }

  &__title {
    font-size: 1.08rem;
    font-weight: 700;
    margin: 0;
    color: #3a3a4a;
  }

  &__content {
    padding: 1.5rem 1.5rem 1.2rem 1.5rem;
  }
}

.user-showcase {
  display: flex;
  gap: 1.2rem;

  .user-avatar {
    flex-shrink: 0;
  }

  .user-info {
    flex: 1;
  }

  .user-name {
    font-size: 1.08rem;
    font-weight: 700;
    margin-top: 0;
    margin-bottom: 8px;
    color: #3a3a4a;
  }

  .user-stats {
    font-size: 0.97rem;
    color: #7b7b8b;
    margin-bottom: 8px;
  }

  .user-quote {
    font-style: italic;
    margin-bottom: 1.2rem;
    color: #3a3a4a;
  }

  .user-actions {
    display: flex;
    gap: 8px;
  }
}

.topic-showcase {
  .topic-title {
    font-size: 1.08rem;
    font-weight: 700;
    margin-top: 0;
    margin-bottom: 8px;
    color: #3a3a4a;
  }

  .topic-stats {
    font-size: 0.97rem;
    color: #7b7b8b;
    margin-bottom: 1.2rem;
  }

  .topic-votes {
    margin-bottom: 1.2rem;

    .vote-bar {
      display: flex;
      height: 24px;
      border-radius: 4px;
      overflow: hidden;

      &__support {
        background: linear-gradient(90deg, #667eea 0%, #4caf50 100%);
        color: white;
        text-align: center;
        line-height: 24px;
        font-size: 12px;
        font-weight: 600;
        transition: width 0.3s ease-in-out;
        width: var(--support-width, 50%);
      }

      &__oppose {
        background: linear-gradient(90deg, #f44336 0%, #764ba2 100%);
        color: white;
        text-align: center;
        line-height: 24px;
        font-size: 12px;
        font-weight: 600;
        transition: width 0.3s ease-in-out;
        width: var(--oppose-width, 50%);
      }
    }

  }

  .topic-actions {
    display: flex;
    gap: 8px;
  }
}

// 响应式调整
@media (max-width: 900px) {
  .hero-section {
    padding: 64px 0 32px 0;
  }

  .features-section,
  .live-debates-section,
  .ai-models-section,
  .mind-map-section,
  .community-section {
    padding: 40px 0 24px 0;
  }
}

@media (max-width: 600px) {
  .hero-section {
    padding: 32px 0 16px 0;
  }

  .features-section,
  .live-debates-section,
  .ai-models-section,
  .mind-map-section,
  .community-section {
    padding: 20px 0 12px 0;
  }

  .features-grid,
  .live-debates-grid,
  .models-grid,
  .community-cards {
    grid-template-columns: 1fr;
  }
}
