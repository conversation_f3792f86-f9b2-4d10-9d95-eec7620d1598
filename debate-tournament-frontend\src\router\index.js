import { createRouter, createWebHistory } from 'vue-router';
import { useAiModelStore } from '@/store/aiModel';
import Settings from '@/views/common/Settings.vue';

// 路由懒加载
const Home = () => import('@/views/common/Home.vue');
const DebateHall = () => import('@/views/debate/DebateHall.vue');
const AIDebateArena = () => import('@/views/ai/AIDebateArena.vue');
const AIModelSelection = () => import('@/views/ai/AIModelSelection.vue');
const CustomAIModel = () => import('@/views/ai/CustomAIModel.vue');
const Login = () => import('@/views/auth/Login.vue');
const Register = () => import('@/views/auth/Register.vue');
const UserCenter = () => import('@/views/user/UserCenter.vue');
const About = () => import('@/views/common/About.vue');
const NotFound = () => import('@/views/common/NotFound.vue');
const HelpCenter = () => import('@/views/common/HelpCenter.vue');
const FAQ = () => import('@/views/common/FAQ.vue');
const Contact = () => import('@/views/common/Contact.vue');
const Terms = () => import('@/views/common/Terms.vue');
const Privacy = () => import('@/views/common/Privacy.vue');

// 认证相关路由
const authRoutes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: { requiresGuest: true }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/auth/Register.vue'),
    meta: { requiresGuest: true }
  },
  {
    path: '/forgot-password',
    name: 'ForgotPassword',
    component: () => import('@/views/auth/ForgotPassword.vue'),
    meta: { requiresGuest: true }
  },
  {
    path: '/reset-password',
    name: 'ResetPassword',
    component: () => import('@/views/auth/ResetPassword.vue'),
    meta: { requiresGuest: true }
  },
  {
    path: '/verify-email',
    name: 'VerifyEmail',
    component: () => import('@/views/auth/VerifyEmail.vue')
  }
]

// 资源与支持相关路由
const resourceRoutes = [
  {
    path: '/help-center',
    name: 'HelpCenter',
    component: HelpCenter,
    meta: {
      title: '帮助中心',
      requiresAuth: false
    }
  },
  {
    path: '/faq',
    name: 'FAQ',
    component: FAQ,
    meta: {
      title: '常见问题',
      requiresAuth: false
    }
  },
  {
    path: '/contact',
    name: 'Contact',
    component: Contact,
    meta: {
      title: '联系我们',
      requiresAuth: false
    }
  },
  {
    path: '/terms',
    name: 'Terms',
    component: Terms,
    meta: {
      title: '服务条款',
      requiresAuth: false
    }
  },
  {
    path: '/privacy',
    name: 'Privacy',
    component: Privacy,
    meta: {
      title: '隐私政策',
      requiresAuth: false
    }
  }
]

const routes = [
  ...authRoutes,
  ...resourceRoutes,
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: {
      title: 'AI辩论赛平台 - 首页',
      requiresAuth: false
    }
  },
  {
    path: '/debate-hall',
    name: 'DebateHall',
    component: DebateHall,
    meta: {
      title: '辩论大厅',
      requiresAuth: true
    }
  },
  {
    path: '/debate-arena/:id',
    name: 'AIDebateArena',
    component: AIDebateArena,
    props: true,
    meta: {
      title: 'AI辩论场',
      requiresAuth: true
    }
  },
  {
    path: '/ai-models',
    name: 'AIModelSelection',
    component: AIModelSelection,
    meta: {
      title: 'AI模型选择',
      requiresAuth: true
    }
  },
  {
    path: '/ai-models/custom',
    name: 'CustomAIModel',
    component: CustomAIModel,
    meta: {
      title: '自定义AI模型',
      requiresAuth: true
    }
  },
  {
    path: '/user',
    name: 'UserCenter',
    component: UserCenter,
    meta: {
      title: '个人中心',
      requiresAuth: true
    }
  },
  {
    path: '/about',
    name: 'About',
    component: About,
    meta: {
      title: '关于我们',
      requiresAuth: false
    }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: Settings
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound,
    meta: {
      title: '页面未找到',
      requiresAuth: false
    }
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { top: 0 };
    }
  }
});

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  document.title = `${to.meta.title} - AI辩论赛平台`;

  // 检查是否需要登录权限
  if (to.meta.requiresAuth) {
    const token = localStorage.getItem('token');
    if (!token) {
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      });
      return;
    }

    // 如果是进入辩论场景，检查是否已选择所有AI模型
    if (to.name === 'AIDebateArena') {
      const aiModelStore = useAiModelStore();
      if (!aiModelStore.isModelSelectionComplete) {
        next({
          path: '/ai-models',
          query: { redirect: to.fullPath }
        });
        return;
      }
    }
  }
  next();
});

export default router;
