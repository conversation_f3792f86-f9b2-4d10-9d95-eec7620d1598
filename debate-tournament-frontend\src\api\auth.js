import request from './index'

// 设置基础URL
const API_URL = '/auth';

/**
 * 用户登录
 * @param {Object} credentials - 登录凭证
 * @param {string} credentials.username - 用户名
 * @param {string} credentials.password - 密码
 * @returns {Promise} - 返回登录结果
 */
export function login(data) {
  return request.post('/auth/login', data, { forceNoEncrypt: true })
}

/**
 * 用户注册
 * @param {Object} userData - 用户数据
 * @param {string} userData.username - 用户名
 * @param {string} userData.email - 邮箱
 * @param {string} userData.password - 密码
 * @returns {Promise} - 返回注册结果
 */
export function register(data) {
  return request.post('/auth/register', data)
}

/**
 * 用户登出
 * @returns {Promise} - 返回登出结果
 */
export function logout() {
  return request.post('/auth/logout')
}

/**
 * 获取用户信息
 * @returns {Promise} - 返回用户信息
 */
export function getUserInfo() {
  return request.get('/auth/me')
}

/**
 * 更新用户信息
 * @param {Object} userData - 用户数据
 * @returns {Promise} - 返回更新结果
 */
export function updateUserInfo(userData) {
  return request.put('/auth/me', userData)
}

/**
 * 修改密码
 * @param {Object} passwordData - 密码数据
 * @param {string} passwordData.oldPassword - 旧密码
 * @param {string} passwordData.newPassword - 新密码
 * @returns {Promise} - 返回修改结果
 */
export function changePassword(passwordData) {
  return request.put('/auth/password', passwordData)
}

// 生成验证码
export function generateCaptcha() {
  return request.get('/auth/captcha/generate')
}

// 获取验证码图片
export function getCaptchaImage(sessionId, width = 120, height = 40) {
  return request.get(`/auth/captcha/image?sessionId=${sessionId}&width=${width}&height=${height}`)
}

// 验证验证码
export function verifyCaptcha(sessionId, code) {
  return request.post(`/auth/captcha/verify?sessionId=${sessionId}&code=${code}`)
}

// 刷新验证码
export function refreshCaptcha(sessionId) {
  return request.post(`/auth/captcha/refresh?sessionId=${sessionId}`)
}

// 忘记密码
export function forgotPassword(data) {
  return request.post('/auth/forgot-password', data)
}

// 重置密码
export function resetPassword(data) {
  return request.post('/auth/reset-password', data)
}

// 验证邮箱
export function verifyEmail(token) {
  return request.post('/auth/verify-email', { token })
}

// 重新发送验证邮件
export function resendVerificationEmail(email) {
  return request.post('/auth/resend-verification', { email })
}
