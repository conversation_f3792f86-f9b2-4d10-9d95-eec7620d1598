package com.debate_ournament.users.controller;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.debate_ournament.dto.common.ApiResponse;
import com.debate_ournament.users.service.FileCleanupService;

/**
 * 文件管理控制器
 */
@RestController
@RequestMapping("/files")
public class FileManagementController {

    @Autowired
    private FileCleanupService fileCleanupService;

    /**
     * 获取文件使用情况
     */
    @GetMapping("/usage")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<FileCleanupService.FileUsageInfo>> getFileUsage() {
        try {
            FileCleanupService.FileUsageInfo usageInfo = fileCleanupService.getFileUsageInfo();
            return ResponseEntity.ok(ApiResponse.success(usageInfo));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("获取文件使用情况失败: " + e.getMessage()));
        }
    }

    /**
     * 手动执行文件清理
     */
    @PostMapping("/cleanup")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> manualCleanup() {
        try {
            // 启动异步清理任务
            fileCleanupService.cleanupOrphanedFiles();

            Map<String, Object> result = new HashMap<>();
            result.put("message", "文件清理任务已启动");
            result.put("async", true);

            // 如需同步等待结果，可以使用：
            // CompletableFuture<Integer> future = fileCleanupService.cleanupOrphanedFiles();
            // Integer deletedCount = future.get();
            // result.put("deletedCount", deletedCount);

            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("启动文件清理失败: " + e.getMessage()));
        }
    }

    /**
     * 获取文件系统状态
     */
    @GetMapping("/status")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getFileSystemStatus() {
        try {
            Map<String, Object> status = new HashMap<>();

            FileCleanupService.FileUsageInfo usageInfo = fileCleanupService.getFileUsageInfo();
            status.put("usage", usageInfo);
            status.put("timestamp", System.currentTimeMillis());

            // 添加系统信息
            Runtime runtime = Runtime.getRuntime();
            Map<String, Object> systemInfo = new HashMap<>();
            systemInfo.put("maxMemory", runtime.maxMemory());
            systemInfo.put("totalMemory", runtime.totalMemory());
            systemInfo.put("freeMemory", runtime.freeMemory());
            systemInfo.put("availableProcessors", runtime.availableProcessors());
            status.put("system", systemInfo);

            return ResponseEntity.ok(ApiResponse.success(status));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("获取文件系统状态失败: " + e.getMessage()));
        }
    }
}
