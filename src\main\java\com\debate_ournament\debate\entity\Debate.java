package com.debate_ournament.debate.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 辩论实体类
 */
@Entity
@Table(name = "debates")
public class Debate {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, length = 500)
    private String title;

    @Column(columnDefinition = "TEXT")
    private String description;

    @Column(length = 100)
    private String category;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private DebateStatus status = DebateStatus.WAITING;

    @Column(name = "creator_id")
    private Long creatorId;

    @Column(name = "creator_username", length = 100)
    private String creatorUsername;

    @Column(name = "max_participants")
    private Integer maxParticipants = 10;

    @Column(name = "current_participants")
    private Integer currentParticipants = 0;

    @Column(name = "supporter_count")
    private Integer supporterCount = 0;

    @Column(name = "opposer_count")
    private Integer opposerCount = 0;

    @Column(name = "judge_count")
    private Integer judgeCount = 0;

    @Column(name = "spectator_count")
    private Integer spectatorCount = 0;

    @Column(name = "view_count")
    private Long viewCount = 0L;

    @Column(name = "like_count")
    private Long likeCount = 0L;

    @Column(name = "comment_count")
    private Long commentCount = 0L;

    @Column(name = "duration_minutes")
    private Integer durationMinutes = 60;

    @Column(name = "turn_duration_minutes")
    private Integer turnDurationMinutes = 3;

    @Column(name = "max_rounds")
    private Integer maxRounds = 5;

    @Column(name = "current_round")
    private Integer currentRound = 0;

    @Column(name = "is_public")
    private Boolean isPublic = true;

    @Column(name = "is_featured")
    private Boolean isFeatured = false;

    @Column(name = "tags")
    private String tags;

    @Column(name = "rules", columnDefinition = "TEXT")
    private String rules;

    @Column(name = "started_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startedAt;

    @Column(name = "ended_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endedAt;

    @CreationTimestamp
    @Column(name = "created_at", updatable = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    // 辩论结果相关字段
    @Column(name = "supporter_score")
    private Double supporterScore = 0.0;

    @Column(name = "opposer_score")
    private Double opposerScore = 0.0;

    @Enumerated(EnumType.STRING)
    @Column(name = "winner_side")
    private DebateSide winnerSide;

    @Column(name = "result_summary", columnDefinition = "TEXT")
    private String resultSummary;

    // 构造函数
    public Debate() {}

    public Debate(String title, String description, String category, Long creatorId, String creatorUsername) {
        this.title = title;
        this.description = description;
        this.category = category;
        this.creatorId = creatorId;
        this.creatorUsername = creatorUsername;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public DebateStatus getStatus() {
        return status;
    }

    public void setStatus(DebateStatus status) {
        this.status = status;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public String getCreatorUsername() {
        return creatorUsername;
    }

    public void setCreatorUsername(String creatorUsername) {
        this.creatorUsername = creatorUsername;
    }

    public Integer getMaxParticipants() {
        return maxParticipants;
    }

    public void setMaxParticipants(Integer maxParticipants) {
        this.maxParticipants = maxParticipants;
    }

    public Integer getCurrentParticipants() {
        return currentParticipants;
    }

    public void setCurrentParticipants(Integer currentParticipants) {
        this.currentParticipants = currentParticipants;
    }

    public Integer getSupporterCount() {
        return supporterCount;
    }

    public void setSupporterCount(Integer supporterCount) {
        this.supporterCount = supporterCount;
    }

    public Integer getOpposerCount() {
        return opposerCount;
    }

    public void setOpposerCount(Integer opposerCount) {
        this.opposerCount = opposerCount;
    }

    public Integer getJudgeCount() {
        return judgeCount;
    }

    public void setJudgeCount(Integer judgeCount) {
        this.judgeCount = judgeCount;
    }

    public Integer getSpectatorCount() {
        return spectatorCount;
    }

    public void setSpectatorCount(Integer spectatorCount) {
        this.spectatorCount = spectatorCount;
    }

    public Long getViewCount() {
        return viewCount;
    }

    public void setViewCount(Long viewCount) {
        this.viewCount = viewCount;
    }

    public Long getLikeCount() {
        return likeCount;
    }

    public void setLikeCount(Long likeCount) {
        this.likeCount = likeCount;
    }

    public Long getCommentCount() {
        return commentCount;
    }

    public void setCommentCount(Long commentCount) {
        this.commentCount = commentCount;
    }

    public Integer getDurationMinutes() {
        return durationMinutes;
    }

    public void setDurationMinutes(Integer durationMinutes) {
        this.durationMinutes = durationMinutes;
    }

    public Integer getTurnDurationMinutes() {
        return turnDurationMinutes;
    }

    public void setTurnDurationMinutes(Integer turnDurationMinutes) {
        this.turnDurationMinutes = turnDurationMinutes;
    }

    public Integer getMaxRounds() {
        return maxRounds;
    }

    public void setMaxRounds(Integer maxRounds) {
        this.maxRounds = maxRounds;
    }

    public Integer getCurrentRound() {
        return currentRound;
    }

    public void setCurrentRound(Integer currentRound) {
        this.currentRound = currentRound;
    }

    public Boolean getIsPublic() {
        return isPublic;
    }

    public void setIsPublic(Boolean isPublic) {
        this.isPublic = isPublic;
    }

    public Boolean getIsFeatured() {
        return isFeatured;
    }

    public void setIsFeatured(Boolean isFeatured) {
        this.isFeatured = isFeatured;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getRules() {
        return rules;
    }

    public void setRules(String rules) {
        this.rules = rules;
    }

    public LocalDateTime getStartedAt() {
        return startedAt;
    }

    public void setStartedAt(LocalDateTime startedAt) {
        this.startedAt = startedAt;
    }

    public LocalDateTime getEndedAt() {
        return endedAt;
    }

    public void setEndedAt(LocalDateTime endedAt) {
        this.endedAt = endedAt;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Double getSupporterScore() {
        return supporterScore;
    }

    public void setSupporterScore(Double supporterScore) {
        this.supporterScore = supporterScore;
    }

    public Double getOpposerScore() {
        return opposerScore;
    }

    public void setOpposerScore(Double opposerScore) {
        this.opposerScore = opposerScore;
    }

    public DebateSide getWinnerSide() {
        return winnerSide;
    }

    public void setWinnerSide(DebateSide winnerSide) {
        this.winnerSide = winnerSide;
    }

    public String getResultSummary() {
        return resultSummary;
    }

    public void setResultSummary(String resultSummary) {
        this.resultSummary = resultSummary;
    }
}
