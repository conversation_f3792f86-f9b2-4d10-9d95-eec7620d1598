-- ========================================
-- 辩论模块数据表创建脚本
-- ========================================
-- 版本: 1.0
-- 创建日期: 2025-01-25
-- 说明: 创建辩论相关的数据表

USE debate_tournament;

-- ========================================
-- 辩论核心数据表
-- ========================================

-- 创建辩论主表
CREATE TABLE IF NOT EXISTS debates (
    id bigint NOT NULL AUTO_INCREMENT COMMENT '辩论主键ID',
    title varchar(500) NOT NULL COMMENT '辩论标题',
    description text COMMENT '辩论描述',
    category varchar(100) COMMENT '辩论分类',
    status varchar(20) NOT NULL DEFAULT 'WAITING' COMMENT '辩论状态（WAITING=等待中，ACTIVE=进行中，COMPLETED=已完成，CANCELLED=已取消）',
    creator_id bigint NOT NULL COMMENT '创建者用户ID',
    creator_username varchar(100) NOT NULL COMMENT '创建者用户名',
    max_participants int NOT NULL DEFAULT 10 COMMENT '最大参与人数',
    current_participants int NOT NULL DEFAULT 0 COMMENT '当前参与人数',
    supporter_count int NOT NULL DEFAULT 0 COMMENT '支持方人数',
    opposer_count int NOT NULL DEFAULT 0 COMMENT '反对方人数',
    judge_count int NOT NULL DEFAULT 0 COMMENT '评委人数',
    spectator_count int NOT NULL DEFAULT 0 COMMENT '观众人数',
    view_count bigint NOT NULL DEFAULT 0 COMMENT '观看次数',
    like_count bigint NOT NULL DEFAULT 0 COMMENT '点赞次数',
    comment_count bigint NOT NULL DEFAULT 0 COMMENT '评论次数',
    duration_minutes int NOT NULL DEFAULT 60 COMMENT '辩论时长（分钟）',
    turn_duration_minutes int NOT NULL DEFAULT 3 COMMENT '每轮发言时长（分钟）',
    max_rounds int NOT NULL DEFAULT 5 COMMENT '最大轮数',
    current_round int NOT NULL DEFAULT 0 COMMENT '当前轮数',
    is_public boolean NOT NULL DEFAULT true COMMENT '是否公开',
    is_featured boolean NOT NULL DEFAULT false COMMENT '是否推荐',
    tags varchar(500) COMMENT '标签（逗号分隔）',
    rules text COMMENT '辩论规则',
    started_at datetime COMMENT '开始时间',
    ended_at datetime COMMENT '结束时间',
    created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    supporter_score decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '支持方得分',
    opposer_score decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '反对方得分',
    winner_side varchar(20) COMMENT '获胜方（SUPPORTER=支持方，OPPOSER=反对方，DRAW=平局）',
    result_summary text COMMENT '结果总结',
    PRIMARY KEY (id),
    KEY idx_status (status) COMMENT '状态索引',
    KEY idx_category (category) COMMENT '分类索引',
    KEY idx_creator_id (creator_id) COMMENT '创建者ID索引',
    KEY idx_creator_username (creator_username) COMMENT '创建者用户名索引',
    KEY idx_is_public (is_public) COMMENT '公开状态索引',
    KEY idx_is_featured (is_featured) COMMENT '推荐状态索引',
    KEY idx_created_at (created_at) COMMENT '创建时间索引',
    KEY idx_started_at (started_at) COMMENT '开始时间索引',
    KEY idx_view_count (view_count) COMMENT '观看次数索引',
    KEY idx_like_count (like_count) COMMENT '点赞次数索引',
    FOREIGN KEY (creator_id) REFERENCES users (id) ON DELETE CASCADE COMMENT '创建者外键约束'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='辩论主表';

-- 创建辩论参与者表
CREATE TABLE IF NOT EXISTS debate_participants (
    id bigint NOT NULL AUTO_INCREMENT COMMENT '参与记录主键ID',
    debate_id bigint NOT NULL COMMENT '辩论ID',
    user_id bigint NOT NULL COMMENT '用户ID',
    username varchar(100) NOT NULL COMMENT '用户名',
    side varchar(20) NOT NULL COMMENT '参与方（SUPPORTER=支持方，OPPOSER=反对方，JUDGE=评委，SPECTATOR=观众）',
    role varchar(20) COMMENT '角色（LEADER=队长，MEMBER=队员）',
    joined_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
    status varchar(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '参与状态（ACTIVE=活跃，INACTIVE=非活跃，REMOVED=已移除）',
    score decimal(5,2) DEFAULT 0.00 COMMENT '个人得分',
    PRIMARY KEY (id),
    UNIQUE KEY uk_debate_user (debate_id, user_id) COMMENT '辩论用户唯一约束',
    KEY idx_debate_id (debate_id) COMMENT '辩论ID索引',
    KEY idx_user_id (user_id) COMMENT '用户ID索引',
    KEY idx_side (side) COMMENT '参与方索引',
    KEY idx_joined_at (joined_at) COMMENT '加入时间索引',
    FOREIGN KEY (debate_id) REFERENCES debates (id) ON DELETE CASCADE COMMENT '辩论外键约束',
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE COMMENT '用户外键约束'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='辩论参与者表';

-- 创建辩论发言表
CREATE TABLE IF NOT EXISTS debate_speeches (
    id bigint NOT NULL AUTO_INCREMENT COMMENT '发言记录主键ID',
    debate_id bigint NOT NULL COMMENT '辩论ID',
    user_id bigint NOT NULL COMMENT '发言用户ID',
    username varchar(100) NOT NULL COMMENT '发言用户名',
    side varchar(20) NOT NULL COMMENT '发言方（SUPPORTER=支持方，OPPOSER=反对方）',
    round_number int NOT NULL COMMENT '轮次号',
    speech_order int NOT NULL COMMENT '发言顺序',
    content text NOT NULL COMMENT '发言内容',
    speech_type varchar(20) NOT NULL DEFAULT 'NORMAL' COMMENT '发言类型（OPENING=开场，NORMAL=正常，CLOSING=结束）',
    duration_seconds int COMMENT '发言时长（秒）',
    created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发言时间',
    score decimal(5,2) DEFAULT 0.00 COMMENT '发言得分',
    PRIMARY KEY (id),
    KEY idx_debate_id (debate_id) COMMENT '辩论ID索引',
    KEY idx_user_id (user_id) COMMENT '用户ID索引',
    KEY idx_side (side) COMMENT '发言方索引',
    KEY idx_round_number (round_number) COMMENT '轮次索引',
    KEY idx_created_at (created_at) COMMENT '发言时间索引',
    FOREIGN KEY (debate_id) REFERENCES debates (id) ON DELETE CASCADE COMMENT '辩论外键约束',
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE COMMENT '用户外键约束'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='辩论发言记录表';

-- 创建辩论投票表
CREATE TABLE IF NOT EXISTS debate_votes (
    id bigint NOT NULL AUTO_INCREMENT COMMENT '投票记录主键ID',
    debate_id bigint NOT NULL COMMENT '辩论ID',
    user_id bigint NOT NULL COMMENT '投票用户ID',
    username varchar(100) NOT NULL COMMENT '投票用户名',
    side varchar(20) NOT NULL COMMENT '投票方（SUPPORTER=支持方，OPPOSER=反对方）',
    vote_type varchar(20) NOT NULL DEFAULT 'FINAL' COMMENT '投票类型（FINAL=最终投票，ROUND=轮次投票）',
    round_number int COMMENT '轮次号（轮次投票时使用）',
    created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '投票时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_debate_user_type (debate_id, user_id, vote_type, round_number) COMMENT '辩论用户投票类型唯一约束',
    KEY idx_debate_id (debate_id) COMMENT '辩论ID索引',
    KEY idx_user_id (user_id) COMMENT '用户ID索引',
    KEY idx_side (side) COMMENT '投票方索引',
    KEY idx_created_at (created_at) COMMENT '投票时间索引',
    FOREIGN KEY (debate_id) REFERENCES debates (id) ON DELETE CASCADE COMMENT '辩论外键约束',
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE COMMENT '用户外键约束'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='辩论投票记录表';

-- 创建辩论评论表
CREATE TABLE IF NOT EXISTS debate_comments (
    id bigint NOT NULL AUTO_INCREMENT COMMENT '评论主键ID',
    debate_id bigint NOT NULL COMMENT '辩论ID',
    user_id bigint NOT NULL COMMENT '评论用户ID',
    username varchar(100) NOT NULL COMMENT '评论用户名',
    content text NOT NULL COMMENT '评论内容',
    parent_id bigint COMMENT '父评论ID（回复时使用）',
    like_count int NOT NULL DEFAULT 0 COMMENT '点赞次数',
    created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '评论时间',
    updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    KEY idx_debate_id (debate_id) COMMENT '辩论ID索引',
    KEY idx_user_id (user_id) COMMENT '用户ID索引',
    KEY idx_parent_id (parent_id) COMMENT '父评论ID索引',
    KEY idx_created_at (created_at) COMMENT '评论时间索引',
    FOREIGN KEY (debate_id) REFERENCES debates (id) ON DELETE CASCADE COMMENT '辩论外键约束',
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE COMMENT '用户外键约束',
    FOREIGN KEY (parent_id) REFERENCES debate_comments (id) ON DELETE CASCADE COMMENT '父评论外键约束'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='辩论评论表';

-- ========================================
-- 插入示例辩论数据
-- ========================================

-- 插入示例辩论
INSERT IGNORE INTO debates (title, description, category, creator_id, creator_username, status, is_public, is_featured, tags) VALUES
('人工智能是否会取代人类工作', '探讨人工智能技术发展对就业市场的影响，以及是否会大规模取代人类工作岗位。', '科技', 1, 'admin', 'WAITING', true, true, '人工智能,就业,科技发展'),
('网络教育是否能完全替代传统教育', '讨论在线教育的优势和局限性，以及是否能够完全取代传统的面对面教育模式。', '教育', 2, 'testuser', 'WAITING', true, false, '在线教育,传统教育,教学模式'),
('社交媒体对青少年的影响利大于弊', '分析社交媒体平台对青少年心理健康、社交能力和学习的正面和负面影响。', '社会', 3, 'debater1', 'ACTIVE', true, false, '社交媒体,青少年,心理健康'),
('环保政策应该优先于经济发展', '探讨在环境保护和经济增长之间如何平衡，以及环保政策的优先级问题。', '环境', 4, 'debater2', 'COMPLETED', true, true, '环保,经济发展,可持续发展');

-- 显示创建结果
SELECT 'Debate Tables Created Successfully' as Status;

-- 显示辩论表记录统计
SELECT 
    'debates' as table_name, COUNT(*) as record_count FROM debates
UNION ALL
SELECT 
    'debate_participants' as table_name, COUNT(*) as record_count FROM debate_participants
UNION ALL
SELECT 
    'debate_speeches' as table_name, COUNT(*) as record_count FROM debate_speeches
UNION ALL
SELECT 
    'debate_votes' as table_name, COUNT(*) as record_count FROM debate_votes
UNION ALL
SELECT 
    'debate_comments' as table_name, COUNT(*) as record_count FROM debate_comments;
