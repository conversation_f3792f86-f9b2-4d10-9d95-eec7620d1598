// 颜色系统
$color-primary: #6750A4;
$color-secondary: #625B71;
$color-surface: #FFFBFE;
$color-surface-variant: #F3F2F7;
$color-on-surface: #1C1B1F;
$color-error: #B3261E;
$color-success: #4CAF50;
$color-warning: #FB8C00;
$color-text-primary: #1C1B1F;
$color-text-secondary: #6B7280;
$color-border: #E5E7EB;
$color-disabled: #9CA3AF;

// 辩论特定颜色
$debate-supporter-color: #1a56db;
$debate-opposer-color: #f59e0b;
$debate-neutral-color: #6B7280;

// 形状系统
$shape-corner-small: 8px;
$shape-corner-medium: 12px;
$shape-corner-large: 16px;

// 边框半径系统（兼容性别名）
$border-radius-sm: $shape-corner-small;
$border-radius-md: $shape-corner-medium;
$border-radius-lg: $shape-corner-large;

// 字体系统
$font-size-h1: 32px;
$font-size-h2: 28px;
$font-size-body: 16px;
$font-size-caption: 12px;

// 间距系统
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;

// 断点系统
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px;

$breakpoints: (
  sm: $breakpoint-sm,
  md: $breakpoint-md,
  lg: $breakpoint-lg,
  xl: $breakpoint-xl
);

// 阴影系统
$shadow-sm: 0 1px 4px rgba(0, 0, 0, 0.08);
$shadow-md: 0 4px 16px rgba(0, 0, 0, 0.12);
