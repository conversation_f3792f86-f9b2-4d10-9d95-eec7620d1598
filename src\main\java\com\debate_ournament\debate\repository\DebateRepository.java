package com.debate_ournament.debate.repository;

import com.debate_ournament.debate.entity.Debate;
import com.debate_ournament.debate.entity.DebateStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 辩论数据访问层
 */
@Repository
public interface DebateRepository extends JpaRepository<Debate, Long> {

    /**
     * 根据状态查找辩论
     */
    Page<Debate> findByStatus(DebateStatus status, Pageable pageable);

    /**
     * 根据分类查找辩论
     */
    Page<Debate> findByCategory(String category, Pageable pageable);

    /**
     * 根据创建者查找辩论
     */
    Page<Debate> findByCreatorUsername(String creatorUsername, Pageable pageable);

    /**
     * 根据创建者ID查找辩论
     */
    Page<Debate> findByCreatorId(Long creatorId, Pageable pageable);

    /**
     * 查找公开的辩论
     */
    Page<Debate> findByIsPublicTrue(Pageable pageable);

    /**
     * 查找推荐的辩论
     */
    Page<Debate> findByIsFeaturedTrue(Pageable pageable);

    /**
     * 根据状态查找实时辩论
     */
    @Query("SELECT d FROM Debate d WHERE d.status IN :statuses ORDER BY d.createdAt DESC")
    Page<Debate> findLiveDebates(@Param("statuses") List<DebateStatus> statuses, Pageable pageable);

    /**
     * 根据观看数查找热门辩论
     */
    @Query("SELECT d FROM Debate d WHERE d.isPublic = true ORDER BY d.viewCount DESC, d.likeCount DESC")
    Page<Debate> findPopularDebates(Pageable pageable);

    /**
     * 复合条件搜索辩论
     */
    @Query("SELECT d FROM Debate d WHERE " +
           "(:category IS NULL OR :category = '' OR d.category = :category) AND " +
           "(:status IS NULL OR :status = '' OR d.status = :status) AND " +
           "(:search IS NULL OR :search = '' OR " +
           "LOWER(d.title) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           "LOWER(d.description) LIKE LOWER(CONCAT('%', :search, '%'))) AND " +
           "d.isPublic = true")
    Page<Debate> findDebatesWithFilters(
        @Param("category") String category,
        @Param("status") String status,
        @Param("search") String search,
        Pageable pageable
    );

    /**
     * 根据标签查找辩论
     */
    @Query("SELECT d FROM Debate d WHERE d.tags LIKE %:tag% AND d.isPublic = true")
    Page<Debate> findByTagsContaining(@Param("tag") String tag, Pageable pageable);

    /**
     * 统计用户创建的辩论数量
     */
    long countByCreatorId(Long creatorId);

    /**
     * 统计用户创建的辩论数量（按状态）
     */
    long countByCreatorIdAndStatus(Long creatorId, DebateStatus status);

    /**
     * 查找最近的辩论
     */
    @Query("SELECT d FROM Debate d WHERE d.isPublic = true ORDER BY d.createdAt DESC")
    Page<Debate> findRecentDebates(Pageable pageable);

    /**
     * 查找即将开始的辩论
     */
    @Query("SELECT d FROM Debate d WHERE d.status = 'WAITING' AND d.isPublic = true ORDER BY d.createdAt ASC")
    Page<Debate> findUpcomingDebates(Pageable pageable);
}
