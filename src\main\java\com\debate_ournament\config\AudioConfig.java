package com.debate_ournament.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 音频相关配置
 */
@Configuration
@ConfigurationProperties(prefix = "app.audio")
public class AudioConfig {

    /**
     * 音频存储路径
     */
    private String storagePath = "./audio";

    /**
     * 音频访问基础URL
     */
    private String baseUrl = "http://localhost:8088/api/audio";

    /**
     * 最大音频文件大小（字节）
     */
    private long maxFileSize = 50 * 1024 * 1024; // 50MB

    /**
     * 支持的音频格式
     */
    private String[] supportedFormats = {"mp3", "wav", "opus", "aac", "flac"};

    /**
     * 默认音频格式
     */
    private String defaultFormat = "mp3";

    /**
     * 默认采样率
     */
    private int defaultSampleRate = 22050;

    /**
     * 默认比特率
     */
    private int defaultBitRate = 128;

    /**
     * 文件清理策略
     */
    private CleanupConfig cleanup = new CleanupConfig();

    // Getters and Setters
    public String getStoragePath() {
        return storagePath;
    }

    public void setStoragePath(String storagePath) {
        this.storagePath = storagePath;
    }

    public String getBaseUrl() {
        return baseUrl;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    public long getMaxFileSize() {
        return maxFileSize;
    }

    public void setMaxFileSize(long maxFileSize) {
        this.maxFileSize = maxFileSize;
    }

    public String[] getSupportedFormats() {
        return supportedFormats;
    }

    public void setSupportedFormats(String[] supportedFormats) {
        this.supportedFormats = supportedFormats;
    }

    public String getDefaultFormat() {
        return defaultFormat;
    }

    public void setDefaultFormat(String defaultFormat) {
        this.defaultFormat = defaultFormat;
    }

    public int getDefaultSampleRate() {
        return defaultSampleRate;
    }

    public void setDefaultSampleRate(int defaultSampleRate) {
        this.defaultSampleRate = defaultSampleRate;
    }

    public int getDefaultBitRate() {
        return defaultBitRate;
    }

    public void setDefaultBitRate(int defaultBitRate) {
        this.defaultBitRate = defaultBitRate;
    }

    public CleanupConfig getCleanup() {
        return cleanup;
    }

    public void setCleanup(CleanupConfig cleanup) {
        this.cleanup = cleanup;
    }

    /**
     * 文件清理配置
     */
    public static class CleanupConfig {
        /**
         * 是否启用自动清理
         */
        private boolean enabled = true;

        /**
         * 文件保留天数
         */
        private int retentionDays = 7;

        /**
         * 清理任务执行间隔（小时）
         */
        private int intervalHours = 24;

        // Getters and Setters
        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public int getRetentionDays() {
            return retentionDays;
        }

        public void setRetentionDays(int retentionDays) {
            this.retentionDays = retentionDays;
        }

        public int getIntervalHours() {
            return intervalHours;
        }

        public void setIntervalHours(int intervalHours) {
            this.intervalHours = intervalHours;
        }
    }
}
