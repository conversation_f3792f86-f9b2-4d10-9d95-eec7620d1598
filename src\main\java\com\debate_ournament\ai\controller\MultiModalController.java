package com.debate_ournament.ai.controller;

import java.util.List;
import java.util.concurrent.CompletableFuture;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.debate_ournament.ai.entity.MediaType;
import com.debate_ournament.ai.entity.MultiModalResponse;
import com.debate_ournament.ai.service.MultiModalService;
import com.debate_ournament.ai.service.MultiModalService.MultiModalRequest;
import com.debate_ournament.ai.service.MultiModalService.MultiModalStats;
import com.debate_ournament.ai.service.TextToSpeechService.VoiceConfig;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

/**
 * 多模态AI控制器
 * 提供文本和语音的多模态AI服务
 */
@RestController
@RequestMapping("/ai/multimodal")
@Tag(name = "多模态AI", description = "多模态AI聊天和语音合成接口")
public class MultiModalController {

    private final MultiModalService multiModalService;

    public MultiModalController(MultiModalService multiModalService) {
        this.multiModalService = multiModalService;
    }

    /**
     * 生成多模态响应
     */
    @PostMapping("/generate")
    @Operation(summary = "生成多模态响应", description = "生成包含文本和语音的AI响应")
    public ResponseEntity<MultiModalResponse> generateMultiModalResponse(
            @RequestHeader("User-Id") Long userId,
            @Valid @RequestBody MultiModalRequest request) {

        MultiModalResponse response = multiModalService.generateMultiModalResponse(userId, request);
        return ResponseEntity.ok(response);
    }

    /**
     * 异步生成多模态响应
     */
    @PostMapping("/generate/async")
    @Operation(summary = "异步生成多模态响应", description = "异步生成包含文本和语音的AI响应")
    public ResponseEntity<CompletableFuture<MultiModalResponse>> generateMultiModalResponseAsync(
            @RequestHeader("User-Id") Long userId,
            @Valid @RequestBody MultiModalRequest request) {

        CompletableFuture<MultiModalResponse> future = multiModalService.generateMultiModalResponseAsync(userId, request);
        return ResponseEntity.ok(future);
    }

    /**
     * 仅生成语音响应
     */
    @PostMapping("/audio-only")
    @Operation(summary = "仅生成语音响应", description = "将文本转换为语音")
    public ResponseEntity<MultiModalResponse> generateAudioOnlyResponse(
            @RequestHeader("User-Id") Long userId,
            @RequestBody AudioOnlyRequest request) {

        MultiModalResponse response = multiModalService.generateAudioOnlyResponse(
            userId, request.getText(), request.getSessionId(),
            request.getTtsConfigId(), request.getVoiceConfig());

        return ResponseEntity.ok(response);
    }

    /**
     * 获取用户的多模态响应列表
     */
    @GetMapping("/responses")
    @Operation(summary = "获取用户响应列表", description = "分页获取用户的多模态响应")
    public ResponseEntity<Page<MultiModalResponse>> getUserResponses(
            @RequestHeader("User-Id") Long userId,
            @Parameter(description = "分页参数") Pageable pageable) {

        Page<MultiModalResponse> responses = multiModalService.getUserResponses(userId, pageable);
        return ResponseEntity.ok(responses);
    }

    /**
     * 获取会话的多模态响应
     */
    @GetMapping("/sessions/{sessionId}/responses")
    @Operation(summary = "获取会话响应", description = "获取指定会话的所有多模态响应")
    public ResponseEntity<List<MultiModalResponse>> getSessionResponses(
            @PathVariable String sessionId) {

        List<MultiModalResponse> responses = multiModalService.getSessionResponses(sessionId);
        return ResponseEntity.ok(responses);
    }

    /**
     * 根据媒体类型获取响应
     */
    @GetMapping("/responses/by-type")
    @Operation(summary = "按类型获取响应", description = "根据媒体类型获取响应")
    public ResponseEntity<List<MultiModalResponse>> getResponsesByMediaType(
            @RequestParam MediaType mediaType) {

        List<MultiModalResponse> responses = multiModalService.getResponsesByMediaType(mediaType);
        return ResponseEntity.ok(responses);
    }

    /**
     * 删除多模态响应
     */
    @DeleteMapping("/responses/{responseId}")
    @Operation(summary = "删除响应", description = "删除指定的多模态响应")
    public ResponseEntity<Void> deleteResponse(
            @RequestHeader("User-Id") Long userId,
            @PathVariable Long responseId) {

        multiModalService.deleteResponse(responseId, userId);
        return ResponseEntity.ok().build();
    }

    /**
     * 获取多模态统计信息
     */
    @GetMapping("/stats")
    @Operation(summary = "获取统计信息", description = "获取用户的多模态使用统计")
    public ResponseEntity<MultiModalStats> getMultiModalStats(
            @RequestHeader("User-Id") Long userId) {

        MultiModalStats stats = multiModalService.getMultiModalStats(userId);
        return ResponseEntity.ok(stats);
    }

    /**
     * 仅音频请求类
     */
    public static class AudioOnlyRequest {
        private String text;
        private String sessionId;
        private Long ttsConfigId;
        private VoiceConfig voiceConfig;

        // Getters and Setters
        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }

        public String getSessionId() {
            return sessionId;
        }

        public void setSessionId(String sessionId) {
            this.sessionId = sessionId;
        }

        public Long getTtsConfigId() {
            return ttsConfigId;
        }

        public void setTtsConfigId(Long ttsConfigId) {
            this.ttsConfigId = ttsConfigId;
        }

        public VoiceConfig getVoiceConfig() {
            return voiceConfig;
        }

        public void setVoiceConfig(VoiceConfig voiceConfig) {
            this.voiceConfig = voiceConfig;
        }
    }
}
