#!/usr/bin/env node

/**
 * 智能开发启动脚本
 * 1. 检测后端服务端口
 * 2. 更新前端配置
 * 3. 启动前端开发服务器
 */

const { spawn } = require('child_process');
const { detectBackendPort, updateViteConfig } = require('./detect-backend');

/**
 * 启动Vite开发服务器
 */
function startViteServer(backendPort) {
  console.log('🚀 启动前端开发服务器...');
  
  const viteProcess = spawn('npm', ['run', 'dev'], {
    stdio: 'inherit',
    shell: true,
    cwd: process.cwd(),
    env: {
      ...process.env,
      DETECTED_BACKEND_PORT: backendPort
    }
  });
  
  viteProcess.on('error', (error) => {
    console.error('❌ 启动前端服务器失败:', error.message);
    process.exit(1);
  });
  
  viteProcess.on('close', (code) => {
    console.log(`前端服务器已停止，退出码: ${code}`);
  });
  
  // 处理进程退出
  process.on('SIGINT', () => {
    console.log('\n🛑 正在停止前端服务器...');
    viteProcess.kill('SIGINT');
  });
  
  process.on('SIGTERM', () => {
    console.log('\n🛑 正在停止前端服务器...');
    viteProcess.kill('SIGTERM');
  });
}

/**
 * 主函数
 */
async function main() {
  console.log('🎯 AI辩论赛平台 - 智能开发启动');
  console.log('================================');
  
  try {
    // 检测后端端口
    const backendPort = await detectBackendPort();
    
    // 更新Vite配置
    updateViteConfig(backendPort);
    
    console.log('================================');
    console.log(`🔗 后端服务: http://localhost:${backendPort}/api`);
    console.log('🔗 前端服务: 即将启动...');
    console.log('================================');
    
    // 启动前端服务器
    startViteServer(backendPort);
    
  } catch (error) {
    console.error('❌ 启动失败:', error.message);
    process.exit(1);
  }
}

// 运行主函数
main();
