-- 创建辩论表
CREATE TABLE IF NOT EXISTS debates (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(500) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    status VARCHAR(50) NOT NULL DEFAULT 'WAITING',
    creator_id BIGINT,
    creator_username VARCHAR(100),
    max_participants INT DEFAULT 10,
    current_participants INT DEFAULT 0,
    supporter_count INT DEFAULT 0,
    opposer_count INT DEFAULT 0,
    judge_count INT DEFAULT 0,
    spectator_count INT DEFAULT 0,
    view_count BIGINT DEFAULT 0,
    like_count BIGINT DEFAULT 0,
    comment_count BIGINT DEFAULT 0,
    duration_minutes INT DEFAULT 60,
    turn_duration_minutes INT DEFAULT 3,
    max_rounds INT DEFAULT 5,
    current_round INT DEFAULT 0,
    is_public BOOLEAN DEFAULT TRUE,
    is_featured BOOLEAN DEFAULT FALSE,
    tags VARCHAR(500),
    rules TEXT,
    started_at TIMESTAMP NULL,
    ended_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    supporter_score DOUBLE DEFAULT 0.0,
    opposer_score DOUBLE DEFAULT 0.0,
    winner_side VARCHAR(50),
    result_summary TEXT,
    INDEX idx_status (status),
    INDEX idx_category (category),
    INDEX idx_creator_id (creator_id),
    INDEX idx_creator_username (creator_username),
    INDEX idx_created_at (created_at),
    INDEX idx_is_public (is_public),
    INDEX idx_is_featured (is_featured)
);

-- 创建辩论参与者表
CREATE TABLE IF NOT EXISTS debate_participants (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    debate_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    username VARCHAR(100) NOT NULL,
    side VARCHAR(50) NOT NULL,
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (debate_id) REFERENCES debates(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_debate (debate_id, user_id),
    INDEX idx_debate_id (debate_id),
    INDEX idx_user_id (user_id),
    INDEX idx_side (side)
);

-- 创建辩论消息表
CREATE TABLE IF NOT EXISTS debate_messages (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    debate_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    username VARCHAR(100) NOT NULL,
    side VARCHAR(50) NOT NULL,
    content TEXT NOT NULL,
    message_type VARCHAR(50) DEFAULT 'ARGUMENT',
    round_number INT DEFAULT 1,
    turn_number INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (debate_id) REFERENCES debates(id) ON DELETE CASCADE,
    INDEX idx_debate_id (debate_id),
    INDEX idx_user_id (user_id),
    INDEX idx_round_number (round_number),
    INDEX idx_created_at (created_at)
);

-- 创建辩论投票表
CREATE TABLE IF NOT EXISTS debate_votes (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    debate_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    username VARCHAR(100) NOT NULL,
    voted_side VARCHAR(50) NOT NULL,
    vote_reason TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (debate_id) REFERENCES debates(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_vote (debate_id, user_id),
    INDEX idx_debate_id (debate_id),
    INDEX idx_voted_side (voted_side)
);

-- 创建辩论收藏表
CREATE TABLE IF NOT EXISTS debate_favorites (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    debate_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    username VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (debate_id) REFERENCES debates(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_favorite (debate_id, user_id),
    INDEX idx_debate_id (debate_id),
    INDEX idx_user_id (user_id)
);

-- 插入一些示例辩论数据
INSERT INTO debates (title, description, category, creator_id, creator_username, status) VALUES
('人工智能是否会取代人类工作', '随着AI技术的快速发展，越来越多的工作岗位面临被自动化取代的风险。这场辩论将探讨AI对就业市场的影响。', '科技', 1, 'admin', 'WAITING'),
('网络教育vs传统教育', '疫情加速了在线教育的发展，但传统面对面教育仍有其不可替代的价值。哪种教育方式更有效？', '教育', 1, 'admin', 'WAITING'),
('环保与经济发展的平衡', '在追求经济增长的同时，如何平衡环境保护的需求？这是一个全球性的挑战。', '环保', 1, 'admin', 'ACTIVE'),
('社交媒体对青少年的影响', '社交媒体为青少年提供了交流平台，但也带来了网络霸凌、隐私泄露等问题。', '社会', 1, 'admin', 'WAITING'),
('远程工作的利与弊', '疫情改变了工作方式，远程工作成为新常态。它真的比传统办公室工作更好吗？', '职场', 1, 'admin', 'COMPLETED');

-- 更新辩论的参与者数量
UPDATE debates SET 
    current_participants = 2,
    supporter_count = 1,
    opposer_count = 1
WHERE id = 3;

UPDATE debates SET 
    current_participants = 4,
    supporter_count = 2,
    opposer_count = 1,
    judge_count = 1,
    supporter_score = 7.5,
    opposer_score = 6.8,
    winner_side = 'SUPPORTER'
WHERE id = 5;
