# AI辩论赛平台 - 前端智能启动脚本
# 自动检测后端端口并启动前端开发服务器

Write-Host "🎯 AI辩论赛平台 - 前端智能启动" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green

# 检查Node.js是否安装
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js版本: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ 错误: 未找到Node.js，请先安装Node.js" -ForegroundColor Red
    exit 1
}

# 检查npm是否可用
try {
    $npmVersion = npm --version
    Write-Host "✅ npm版本: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ 错误: npm不可用" -ForegroundColor Red
    exit 1
}

# 进入前端目录
$frontendDir = "debate-tournament-frontend"
if (Test-Path $frontendDir) {
    Set-Location $frontendDir
    Write-Host "📁 进入前端目录: $frontendDir" -ForegroundColor Blue
} else {
    Write-Host "❌ 错误: 未找到前端目录 $frontendDir" -ForegroundColor Red
    exit 1
}

# 检查是否已安装依赖
if (-not (Test-Path "node_modules")) {
    Write-Host "📦 检测到未安装依赖，正在安装..." -ForegroundColor Yellow
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ 依赖安装失败" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ 依赖安装完成" -ForegroundColor Green
}

Write-Host "================================" -ForegroundColor Green

# 检测后端端口
Write-Host "🔍 正在检测后端服务端口..." -ForegroundColor Blue

$candidatePorts = @(8081, 8080, 8082, 8083, 8084, 8085)
$backendPort = $null

foreach ($port in $candidatePorts) {
    try {
        $connection = Test-NetConnection -ComputerName "localhost" -Port $port -InformationLevel Quiet -WarningAction SilentlyContinue
        if ($connection) {
            Write-Host "📡 检测到端口 $port 被占用，验证是否为后端服务..." -ForegroundColor Yellow
            
            # 尝试访问健康检查端点
            try {
                $response = Invoke-WebRequest -Uri "http://localhost:$port/api/actuator/health" -TimeoutSec 3 -ErrorAction Stop
                if ($response.StatusCode -eq 200) {
                    Write-Host "✅ 确认端口 $port 运行Spring Boot后端服务" -ForegroundColor Green
                    $backendPort = $port
                    break
                }
            } catch {
                Write-Host "⚠️ 端口 $port 被占用但不是Spring Boot服务，继续检测..." -ForegroundColor Yellow
            }
        }
    } catch {
        # 端口不可用，继续检测
        continue
    }
}

if ($null -eq $backendPort) {
    Write-Host "⚠️ 未检测到后端服务，使用默认端口 8081" -ForegroundColor Yellow
    $backendPort = 8081
}

Write-Host "🔗 后端服务: http://localhost:$backendPort/api" -ForegroundColor Cyan

# 设置环境变量
$env:DETECTED_BACKEND_PORT = $backendPort

Write-Host "================================" -ForegroundColor Green
Write-Host "🚀 启动前端开发服务器..." -ForegroundColor Blue
Write-Host "💡 提示: 前端将通过代理转发请求到后端服务" -ForegroundColor Yellow
Write-Host "🔗 API代理: /api -> http://localhost:$backendPort" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Green

# 启动前端开发服务器
try {
    npm run dev
} catch {
    Write-Host "❌ 前端服务器启动失败" -ForegroundColor Red
    exit 1
}
