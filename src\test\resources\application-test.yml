# 测试环境配置
spring:
  # 数据源配置 - 使用H2内存数据库
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=MySQL;DATABASE_TO_LOWER=TRUE;CASE_INSENSITIVE_IDENTIFIERS=TRUE
    username: sa
    password:
    driver-class-name: org.h2.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 1

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: none  # 禁用自动DDL，使用SQL脚本
      naming:
        physical-strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: false
        use_sql_comments: false
    open-in-view: false

  # SQL初始化配置
  sql:
    init:
      mode: always
      schema-locations: classpath:test-schema.sql
      continue-on-error: true

  # H2控制台配置（仅测试环境）
  h2:
    console:
      enabled: true
      path: /h2-console

  # 禁用Redis（测试环境不需要）
  data:
    redis:
      repositories:
        enabled: false

  # 禁用邮件（测试环境不需要）
  mail:
    host: localhost
    port: 25
    username: test
    password: test

  # 缓存配置 - 使用简单缓存
  cache:
    type: simple

# 自定义应用配置
app:
  # 禁用数据库自动初始化（测试环境使用JPA自动创建表）
  database:
    init:
      enabled: false
      force: false
      check-on-startup: false

  # JWT配置（测试用）
  jwt:
    secret: TestSecretKeyForJunitTesting_AtLeast256Bits_ForSecurity_DoNotUseInProduction
    expiration: 3600000 # 1小时
    refresh-expiration: 7200000 # 2小时

  # 验证码配置（测试用）
  captcha:
    enabled: false  # 测试环境禁用验证码
    length: 4
    width: 120
    height: 40
    expiration: 300

  # 邮件配置（测试用）
  mail:
    enabled: false
    smtp:
      host: localhost
      port: 25
      username: test
      password: test

  # 文件存储配置（测试用）
  file:
    storage:
      type: local
      base-path: test-uploads
      avatar:
        path: test-avatars
        max-size: 1MB

  # 加密配置（测试用）
  encryption:
    enabled: false  # 测试环境禁用加密
    auto-generate-on-startup: false

# 日志配置
logging:
  level:
    com.debate_ournament: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
