import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';

// 从环境变量获取后端端口，默认为8081
const getBackendPort = () => {
  return process.env.DETECTED_BACKEND_PORT || 8081;
};

// https://vitejs.dev/config/
export default defineConfig(() => {
  const backendPort = getBackendPort();

  console.log(`🔄 配置API代理: /api -> http://localhost:${backendPort}`);

  return {
    plugins: [vue()],
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src')
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          // 移除 additionalData 以避免与手动导入冲突
        }
      }
    },
    define: {
      // 确保在生产环境中不显示Lit开发模式警告
      'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV),
      // 禁用Lit开发模式
      'global.litIsInSSR': false,
      'globalThis.litIsInSSR': false,
      // 修复GoJS与Vue的冲突
      'global': 'globalThis'
    },
    server: {
      proxy: {
        '/api': {
          target: `http://localhost:${backendPort}`,
          changeOrigin: true,
          secure: false,
          rewrite: (path) => {
            console.log(`📡 代理请求: ${path} -> http://localhost:${backendPort}${path}`);
            return path;
          }
        }
      }
    },
    build: {
      outDir: '../src/main/resources/static',
      emptyOutDir: true,
      chunkSizeWarningLimit: 1500
    }
  };
});
