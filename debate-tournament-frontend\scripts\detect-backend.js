#!/usr/bin/env node

/**
 * 后端端口检测脚本
 * 自动检测后端服务端口并更新环境配置
 */

const net = require('net');
const fs = require('fs');
const path = require('path');

/**
 * 检测端口是否被占用
 */
function isPortInUse(port) {
  return new Promise((resolve) => {
    const server = net.createServer();
    server.listen(port, () => {
      server.once('close', () => {
        resolve(false); // 端口未被占用
      });
      server.close();
    });
    server.on('error', () => {
      resolve(true); // 端口被占用
    });
  });
}

/**
 * 检测Spring Boot健康检查端点
 */
async function checkSpringBootHealth(port) {
  try {
    const response = await fetch(`http://localhost:${port}/api/actuator/health`, {
      method: 'GET',
      signal: AbortSignal.timeout(3000)
    });
    return response.ok;
  } catch (error) {
    return false;
  }
}

/**
 * 动态检测后端端口
 */
async function detectBackendPort() {
  const candidatePorts = [8081, 8080, 8082, 8083, 8084, 8085];
  
  console.log('🔍 开始检测后端服务端口...');
  
  for (const port of candidatePorts) {
    const inUse = await isPortInUse(port);
    if (inUse) {
      console.log(`📡 检测到端口 ${port} 被占用，验证是否为Spring Boot服务...`);
      
      // 验证是否为Spring Boot应用
      const isSpringBoot = await checkSpringBootHealth(port);
      if (isSpringBoot) {
        console.log(`✅ 确认端口 ${port} 运行Spring Boot后端服务`);
        return port;
      } else {
        console.log(`⚠️ 端口 ${port} 被占用但不是Spring Boot服务，继续检测...`);
      }
    }
  }
  
  console.warn('⚠️ 未检测到后端服务，使用默认端口 8081');
  return 8081;
}

/**
 * 更新环境配置文件
 */
function updateEnvFile(port) {
  const envPath = path.join(__dirname, '../.env.development');
  
  try {
    let envContent = fs.readFileSync(envPath, 'utf8');
    
    // 更新API基础URL（开发环境使用代理）
    envContent = envContent.replace(
      /VITE_API_BASE_URL=.*/,
      'VITE_API_BASE_URL=/api'
    );
    
    // 添加检测到的后端端口信息（注释形式）
    const timestamp = new Date().toLocaleString('zh-CN');
    const portInfo = `# 自动检测的后端端口: ${port} (检测时间: ${timestamp})`;
    
    if (!envContent.includes('# 自动检测的后端端口:')) {
      envContent = portInfo + '\n' + envContent;
    } else {
      envContent = envContent.replace(
        /# 自动检测的后端端口:.*/,
        portInfo
      );
    }
    
    fs.writeFileSync(envPath, envContent);
    console.log(`📝 已更新环境配置文件: ${envPath}`);
    
  } catch (error) {
    console.error('❌ 更新环境配置文件失败:', error.message);
  }
}

/**
 * 更新Vite配置文件
 */
function updateViteConfig(port) {
  const viteConfigPath = path.join(__dirname, '../vite.config.js');
  
  try {
    let configContent = fs.readFileSync(viteConfigPath, 'utf8');
    
    // 更新代理目标端口
    configContent = configContent.replace(
      /target: `http:\/\/localhost:\$\{backendPort\}`/,
      `target: 'http://localhost:${port}'`
    );
    
    // 或者直接替换硬编码的端口
    configContent = configContent.replace(
      /target: ['"`]http:\/\/localhost:\d+['"`]/g,
      `target: 'http://localhost:${port}'`
    );
    
    fs.writeFileSync(viteConfigPath, configContent);
    console.log(`⚙️ 已更新Vite配置文件代理端口: ${port}`);
    
  } catch (error) {
    console.error('❌ 更新Vite配置文件失败:', error.message);
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    const backendPort = await detectBackendPort();
    
    // 更新配置文件
    updateEnvFile(backendPort);
    updateViteConfig(backendPort);
    
    console.log(`🎉 后端端口检测完成: ${backendPort}`);
    console.log('💡 提示: 前端将通过代理转发请求到后端服务');
    
    // 输出端口信息供其他脚本使用
    process.stdout.write(backendPort.toString());
    
  } catch (error) {
    console.error('❌ 端口检测失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { detectBackendPort, updateEnvFile, updateViteConfig };
