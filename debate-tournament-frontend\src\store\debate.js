import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { getDebates, getDebateById, createDebate as createDebateAPI } from '@/api/debate';

export const useDebateStore = defineStore('debate', () => {
  // 状态
  const debates = ref([]);
  const currentDebate = ref(null);
  const loading = ref(false);
  const error = ref(null);
  const filters = ref({
    category: '',
    status: '',
    search: '',
    page: 1,
    limit: 10
  });

  // 计算属性
  const filteredDebates = computed(() => {
    // 实际项目中应该在后端进行过滤，这里仅做示例
    return debates.value.filter(debate => {
      let match = true;

      if (filters.value.category && debate.category !== filters.value.category) {
        match = false;
      }

      if (filters.value.status && debate.status !== filters.value.status) {
        match = false;
      }

      if (filters.value.search && !debate.title.toLowerCase().includes(filters.value.search.toLowerCase())) {
        match = false;
      }

      return match;
    });
  });

  const totalPages = computed(() => {
    return Math.ceil(filteredDebates.value.length / filters.value.limit);
  });

  const paginatedDebates = computed(() => {
    const start = (filters.value.page - 1) * filters.value.limit;
    const end = start + filters.value.limit;
    return filteredDebates.value.slice(start, end);
  });

  // 动作
  async function fetchDebates() {
    loading.value = true;
    error.value = null;

    try {
      const response = await getDebates(filters.value);

      if (response.code === 200) {
        // 后端返回的是分页数据，content字段包含辩论数组
        debates.value = response.data.content || response.data.debates || response.data || [];
      } else {
        throw new Error(response.message || '获取辩论列表失败');
      }
    } catch (err) {
      error.value = err.response?.data?.message || err.message || '获取辩论列表失败';
      console.error('获取辩论列表失败', err);
    } finally {
      loading.value = false;
    }
  }

  async function fetchDebateById(id) {
    loading.value = true;
    error.value = null;

    try {
      const response = await getDebateById(id);

      if (response.code === 200) {
        currentDebate.value = response.data;
      } else {
        throw new Error(response.message || '获取辩论详情失败');
      }
    } catch (err) {
      error.value = err.response?.data?.message || err.message || '获取辩论详情失败';
      console.error('获取辩论详情失败', err);
    } finally {
      loading.value = false;
    }
  }

  async function createDebate(debateData) {
    loading.value = true;
    error.value = null;

    try {
      const response = await createDebateAPI(debateData);

      if (response.code === 200) {
        // 添加到列表中
        debates.value.unshift(response.data);
        return response.data;
      } else {
        throw new Error(response.message || '创建辩论失败');
      }
    } catch (err) {
      error.value = err.response?.data?.message || err.message || '创建辩论失败';
      console.error('创建辩论失败', err);
      return null;
    } finally {
      loading.value = false;
    }
  }

  function updateFilters(newFilters) {
    filters.value = { ...filters.value, ...newFilters };
    // 重置为第一页
    if (newFilters.category !== undefined || newFilters.status !== undefined || newFilters.search !== undefined) {
      filters.value.page = 1;
    }
  }

  function resetFilters() {
    filters.value = {
      category: '',
      status: '',
      search: '',
      page: 1,
      limit: 10
    };
  }

  return {
    debates,
    currentDebate,
    loading,
    error,
    filters,
    filteredDebates,
    totalPages,
    paginatedDebates,
    fetchDebates,
    fetchDebateById,
    createDebate,
    updateFilters,
    resetFilters
  };
});
