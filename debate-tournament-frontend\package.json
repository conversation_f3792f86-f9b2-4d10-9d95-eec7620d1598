{"name": "debate-tournament-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:auto": "node scripts/start-dev.js", "detect-backend": "node scripts/detect-backend.js", "build": "tsc && vite build", "preview": "vite preview"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.4", "sass": "^1.89.0", "typescript": "~5.8.3", "vite": "^6.3.5"}, "dependencies": {"@tresjs/core": "^4.3.5", "axios": "^1.9.0", "crypto-js": "^4.2.0", "element-plus": "^2.9.11", "gojs": "^2.3.13", "jsencrypt": "^3.3.2", "mdui": "^2.1.4", "pinia": "^3.0.2", "socket.io-client": "^4.7.4", "three": "^0.176.0", "vue-router": "^4.5.1"}}