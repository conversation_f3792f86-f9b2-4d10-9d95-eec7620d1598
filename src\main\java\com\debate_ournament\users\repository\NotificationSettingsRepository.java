package com.debate_ournament.users.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.debate_ournament.users.entity.NotificationSettings;
import com.debate_ournament.users.entity.User;

/**
 * 通知设置数据访问接口
 */
@Repository
public interface NotificationSettingsRepository extends JpaRepository<NotificationSettings, Long> {

    /**
     * 根据用户查找通知设置
     */
    Optional<NotificationSettings> findByUser(User user);

    /**
     * 根据用户ID查找通知设置
     */
    Optional<NotificationSettings> findByUserId(Long userId);

    /**
     * 查找启用邮件通知的用户设置
     */
    List<NotificationSettings> findByEmailNotificationsTrue();

    /**
     * 查找启用系统公告通知的用户设置
     */
    List<NotificationSettings> findBySystemAnnouncementsTrue();

    /**
     * 查找特定邮件频率的用户设置
     */
    List<NotificationSettings> findByEmailFrequency(String frequency);

    /**
     * 查找当前处于安静时间的用户设置
     */
    @Query("SELECT ns FROM NotificationSettings ns WHERE :currentHour BETWEEN ns.quietHoursStart AND ns.quietHoursEnd")
    List<NotificationSettings> findUsersInQuietHours(@Param("currentHour") Integer currentHour);

    /**
     * 检查用户是否已有通知设置
     */
    boolean existsByUser(User user);

    /**
     * 删除用户的通知设置
     */
    void deleteByUser(User user);

    /**
     * 批量更新用户的邮件通知设置
     */
    @Modifying
    @Query("UPDATE NotificationSettings ns SET ns.emailNotifications = :enabled WHERE ns.user.id IN :userIds")
    int updateEmailNotifications(@Param("userIds") List<Long> userIds, @Param("enabled") boolean enabled);

    /**
     * 批量更新用户的系统公告通知设置
     */
    @Modifying
    @Query("UPDATE NotificationSettings ns SET ns.systemAnnouncements = :enabled WHERE ns.user.id IN :userIds")
    int updateSystemAnnouncements(@Param("userIds") List<Long> userIds, @Param("enabled") boolean enabled);
}
