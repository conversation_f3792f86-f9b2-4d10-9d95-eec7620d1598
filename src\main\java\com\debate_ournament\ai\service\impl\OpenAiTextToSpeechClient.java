package com.debate_ournament.ai.service.impl;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.debate_ournament.ai.entity.AiConfig;
import com.debate_ournament.ai.entity.AiProvider;
import com.debate_ournament.ai.service.TextToSpeechService;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * OpenAI文本转语音服务实现
 */
@Service
public class OpenAiTextToSpeechClient implements TextToSpeechService {

    private static final Logger logger = LoggerFactory.getLogger(OpenAiTextToSpeechClient.class);

    @Value("${app.ai.audio.storage-path:./audio}")
    private String audioStoragePath;

    @Value("${app.ai.audio.base-url:http://localhost:808/api/audio}")
    private String audioBaseUrl;

    private final HttpClient httpClient;
    private final ObjectMapper objectMapper;

    public OpenAiTextToSpeechClient() {
        this.httpClient = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(30))
                .build();
        this.objectMapper = new ObjectMapper();
    }

    @Override
    public String synthesizeText(String text, VoiceConfig voiceConfig, AiConfig aiConfig) {
        try {
            if (!aiConfig.getProvider().equals(AiProvider.OPENAI)) {
                throw new IllegalArgumentException("此客户端只支持OpenAI提供商");
            }

            // 构建请求
            String requestBody = buildTTSRequest(text, voiceConfig);
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(java.net.URI.create(aiConfig.getBaseUrl() + "/audio/speech"))
                    .header("Authorization", "Bearer " + aiConfig.getApiKey())
                    .header("Content-Type", "application/json")
                    .POST(HttpRequest.BodyPublishers.ofString(requestBody))
                    .build();

            // 发送请求
            HttpResponse<InputStream> response = httpClient.send(request,
                    HttpResponse.BodyHandlers.ofInputStream());

            if (response.statusCode() != 200) {
                throw new RuntimeException("TTS请求失败: " + response.statusCode());
            }

            // 保存音频文件
            String fileName = UUID.randomUUID().toString() + "." + voiceConfig.getFormat();
            Path audioPath = saveAudioFile(response.body(), fileName);

            // 返回音频URL
            return audioBaseUrl + "/" + fileName;

        } catch (Exception e) {
            logger.error("OpenAI TTS合成失败", e);
            throw new RuntimeException("语音合成失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void synthesizeTextAsync(String text, VoiceConfig voiceConfig, AiConfig aiConfig,
                                   SynthesisCallback callback) {
        CompletableFuture.supplyAsync(() -> {
            try {
                callback.onProgress(0);
                String audioUrl = synthesizeText(text, voiceConfig, aiConfig);
                callback.onProgress(50);

                // 获取音频信息
                String fileName = audioUrl.substring(audioUrl.lastIndexOf("/") + 1);
                Path audioPath = Paths.get(audioStoragePath, fileName);
                AudioInfo audioInfo = getAudioInfo(audioPath.toFile());

                callback.onProgress(100);
                callback.onSuccess(audioUrl, audioInfo.getDuration(), audioInfo.getFormat());
                return audioUrl;
            } catch (Exception e) {
                callback.onError(e.getMessage());
                throw new RuntimeException(e);
            }
        });
    }

    @Override
    public CompletableFuture<List<String>> synthesizeTexts(List<String> texts, VoiceConfig voiceConfig,
                                                          AiConfig aiConfig) {
        return CompletableFuture.supplyAsync(() -> {
            return texts.stream()
                    .map(text -> synthesizeText(text, voiceConfig, aiConfig))
                    .toList();
        });
    }

    @Override
    public List<String> getSupportedVoices(AiConfig aiConfig) {
        // OpenAI支持的语音列表
        return Arrays.asList("alloy", "echo", "fable", "onyx", "nova", "shimmer");
    }

    @Override
    public List<String> getSupportedLanguages(AiConfig aiConfig) {
        // OpenAI支持的语言列表
        return Arrays.asList("zh-CN", "en-US", "es-ES", "fr-FR", "de-DE", "ja-JP", "ko-KR");
    }

    @Override
    public List<String> getSupportedFormats(AiConfig aiConfig) {
        // OpenAI支持的音频格式
        return Arrays.asList("mp3", "opus", "aac", "flac");
    }

    @Override
    public Integer estimateAudioDuration(String text, VoiceConfig voiceConfig) {
        // 根据文本长度和语速估算时长
        int characterCount = text.length();
        double wordsPerMinute = 150 * voiceConfig.getSpeed(); // 基础语速调整
        double charactersPerMinute = wordsPerMinute * 5; // 平均每个词5个字符
        return (int) Math.ceil((characterCount / charactersPerMinute) * 60);
    }

    @Override
    public AudioInfo getAudioInfo(File audioFile) {
        AudioInfo info = new AudioInfo();
        try {
            info.setFileSize(audioFile.length());
            info.setFormat(getFileExtension(audioFile.getName()));

            // 简单的时长估算（实际项目中可能需要音频库来准确获取）
            // 这里使用文件大小粗略估算
            long fileSizeKB = audioFile.length() / 1024;
            int estimatedDuration = (int) (fileSizeKB / 16); // 假设16KB/秒的比特率
            info.setDuration(Math.max(1, estimatedDuration));

            info.setSampleRate(22050); // OpenAI默认采样率
            info.setBitRate(128); // 假设比特率

        } catch (Exception e) {
            logger.warn("获取音频信息失败: " + e.getMessage());
        }
        return info;
    }

    @Override
    public boolean testConnection(AiConfig aiConfig) {
        try {
            // 发送简单的测试请求
            String testText = "测试";
            VoiceConfig testConfig = new VoiceConfig();
            testConfig.setVoice("alloy");
            testConfig.setFormat("mp3");

            synthesizeText(testText, testConfig, aiConfig);
            return true;
        } catch (Exception e) {
            logger.warn("OpenAI TTS连接测试失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 构建TTS请求JSON
     */
    private String buildTTSRequest(String text, VoiceConfig voiceConfig) {
        try {
            return objectMapper.writeValueAsString(Map.of(
                "model", "tts-1",
                "input", text,
                "voice", voiceConfig.getVoice(),
                "response_format", voiceConfig.getFormat(),
                "speed", voiceConfig.getSpeed()
            ));
        } catch (Exception e) {
            throw new RuntimeException("构建TTS请求失败", e);
        }
    }

    /**
     * 保存音频文件
     */
    private Path saveAudioFile(InputStream audioStream, String fileName) {
        try {
            // 确保目录存在
            Path storageDir = Paths.get(audioStoragePath);
            if (!Files.exists(storageDir)) {
                Files.createDirectories(storageDir);
            }

            Path audioPath = storageDir.resolve(fileName);

            try (FileOutputStream fos = new FileOutputStream(audioPath.toFile())) {
                audioStream.transferTo(fos);
            }

            return audioPath;
        } catch (Exception e) {
            throw new RuntimeException("保存音频文件失败", e);
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex > 0 ? fileName.substring(lastDotIndex + 1) : "";
    }

    // 需要导入Map
    private static class Map {
        public static java.util.Map<String, Object> of(String k1, Object v1, String k2, Object v2,
                                                       String k3, Object v3, String k4, Object v4,
                                                       String k5, Object v5) {
            java.util.Map<String, Object> map = new java.util.HashMap<>();
            map.put(k1, v1);
            map.put(k2, v2);
            map.put(k3, v3);
            map.put(k4, v4);
            map.put(k5, v5);
            return map;
        }
    }
}
