package com.debate_ournament.debate.service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Map;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.debate_ournament.debate.entity.Debate;
import com.debate_ournament.debate.entity.DebateStatus;
import com.debate_ournament.debate.repository.DebateRepository;
import com.debate_ournament.users.entity.User;
import com.debate_ournament.users.repository.UserRepository;

/**
 * 辩论服务类
 */
@Service
@Transactional
public class DebateService {

    @Autowired
    private DebateRepository debateRepository;

    @Autowired
    private UserRepository userRepository;

    /**
     * 获取辩论列表
     */
    public Page<Debate> getDebates(String category, String status, String search, Pageable pageable) {
        // 如果没有筛选条件，返回所有公开辩论
        if ((category == null || category.trim().isEmpty()) &&
            (status == null || status.trim().isEmpty()) &&
            (search == null || search.trim().isEmpty())) {
            return debateRepository.findByIsPublicTrue(pageable);
        }

        // 转换状态字符串为枚举
        String statusParam = null;
        if (status != null && !status.trim().isEmpty()) {
            try {
                DebateStatus.valueOf(status.toUpperCase());
                statusParam = status.toUpperCase();
            } catch (IllegalArgumentException e) {
                // 如果状态无效，忽略该筛选条件
                statusParam = null;
            }
        }

        return debateRepository.findDebatesWithFilters(
            category != null && !category.trim().isEmpty() ? category : null,
            statusParam,
            search != null && !search.trim().isEmpty() ? search : null,
            pageable
        );
    }

    /**
     * 根据ID获取辩论详情
     */
    public Optional<Debate> getDebateById(Long id) {
        Optional<Debate> debate = debateRepository.findById(id);

        // 增加浏览量
        if (debate.isPresent()) {
            Debate d = debate.get();
            d.setViewCount(d.getViewCount() + 1);
            debateRepository.save(d);
        }

        return debate;
    }

    /**
     * 创建辩论
     */
    public Debate createDebate(Map<String, Object> debateData, String username) {
        // 查找用户
        Optional<User> userOpt = userRepository.findByUsername(username);
        if (!userOpt.isPresent()) {
            throw new RuntimeException("用户不存在");
        }

        User user = userOpt.get();

        // 创建辩论对象
        Debate debate = new Debate();
        debate.setTitle((String) debateData.get("title"));
        debate.setDescription((String) debateData.get("description"));
        debate.setCategory((String) debateData.get("category"));
        debate.setCreatorId(user.getId());
        debate.setCreatorUsername(user.getUsername());

        // 设置可选参数
        if (debateData.containsKey("maxParticipants")) {
            debate.setMaxParticipants((Integer) debateData.get("maxParticipants"));
        }
        if (debateData.containsKey("durationMinutes")) {
            debate.setDurationMinutes((Integer) debateData.get("durationMinutes"));
        }
        if (debateData.containsKey("turnDurationMinutes")) {
            debate.setTurnDurationMinutes((Integer) debateData.get("turnDurationMinutes"));
        }
        if (debateData.containsKey("maxRounds")) {
            debate.setMaxRounds((Integer) debateData.get("maxRounds"));
        }
        if (debateData.containsKey("isPublic")) {
            debate.setIsPublic((Boolean) debateData.get("isPublic"));
        }
        if (debateData.containsKey("tags")) {
            debate.setTags((String) debateData.get("tags"));
        }
        if (debateData.containsKey("rules")) {
            debate.setRules((String) debateData.get("rules"));
        }

        return debateRepository.save(debate);
    }

    /**
     * 更新辩论
     */
    public Debate updateDebate(Long id, Map<String, Object> debateData, String username) {
        Optional<Debate> debateOpt = debateRepository.findById(id);
        if (!debateOpt.isPresent()) {
            throw new RuntimeException("辩论不存在");
        }

        Debate debate = debateOpt.get();

        // 检查权限（只有创建者可以修改）
        if (!debate.getCreatorUsername().equals(username)) {
            throw new RuntimeException("没有权限修改此辩论");
        }

        // 更新字段
        if (debateData.containsKey("title")) {
            debate.setTitle((String) debateData.get("title"));
        }
        if (debateData.containsKey("description")) {
            debate.setDescription((String) debateData.get("description"));
        }
        if (debateData.containsKey("category")) {
            debate.setCategory((String) debateData.get("category"));
        }
        if (debateData.containsKey("maxParticipants")) {
            debate.setMaxParticipants((Integer) debateData.get("maxParticipants"));
        }
        if (debateData.containsKey("isPublic")) {
            debate.setIsPublic((Boolean) debateData.get("isPublic"));
        }
        if (debateData.containsKey("tags")) {
            debate.setTags((String) debateData.get("tags"));
        }
        if (debateData.containsKey("rules")) {
            debate.setRules((String) debateData.get("rules"));
        }

        return debateRepository.save(debate);
    }

    /**
     * 删除辩论
     */
    public void deleteDebate(Long id, String username) {
        Optional<Debate> debateOpt = debateRepository.findById(id);
        if (!debateOpt.isPresent()) {
            throw new RuntimeException("辩论不存在");
        }

        Debate debate = debateOpt.get();

        // 检查权限（只有创建者可以删除）
        if (!debate.getCreatorUsername().equals(username)) {
            throw new RuntimeException("没有权限删除此辩论");
        }

        // 只有等待中的辩论可以删除
        if (debate.getStatus() != DebateStatus.WAITING) {
            throw new RuntimeException("只能删除等待中的辩论");
        }

        debateRepository.delete(debate);
    }

    /**
     * 加入辩论
     */
    public void joinDebate(Long id, String username, String side) {
        Optional<Debate> debateOpt = debateRepository.findById(id);
        if (!debateOpt.isPresent()) {
            throw new RuntimeException("辩论不存在");
        }

        Debate debate = debateOpt.get();

        // 检查辩论状态
        if (debate.getStatus() != DebateStatus.WAITING) {
            throw new RuntimeException("只能加入等待中的辩论");
        }

        // 检查参与人数
        if (debate.getCurrentParticipants() >= debate.getMaxParticipants()) {
            throw new RuntimeException("辩论人数已满");
        }

        // 根据选择的角色增加对应计数
        switch (side.toLowerCase()) {
            case "supporter":
                debate.setSupporterCount(debate.getSupporterCount() + 1);
                break;
            case "opposer":
                debate.setOpposerCount(debate.getOpposerCount() + 1);
                break;
            case "judge":
                debate.setJudgeCount(debate.getJudgeCount() + 1);
                break;
            case "spectator":
                debate.setSpectatorCount(debate.getSpectatorCount() + 1);
                break;
            default:
                throw new RuntimeException("无效的角色选择");
        }

        debate.setCurrentParticipants(debate.getCurrentParticipants() + 1);
        debateRepository.save(debate);
    }

    /**
     * 获取实时辩论列表
     */
    public Page<Debate> getLiveDebates(Pageable pageable) {
        return debateRepository.findLiveDebates(
            Arrays.asList(DebateStatus.ACTIVE, DebateStatus.WAITING),
            pageable
        );
    }

    /**
     * 获取热门辩论列表
     */
    public Page<Debate> getPopularDebates(Pageable pageable) {
        return debateRepository.findPopularDebates(pageable);
    }

    /**
     * 获取用户参与的辩论
     */
    public Page<Debate> getUserDebates(String username, Pageable pageable) {
        return debateRepository.findByCreatorUsername(username, pageable);
    }

    /**
     * 开始辩论
     */
    public void startDebate(Long id, String username) {
        Optional<Debate> debateOpt = debateRepository.findById(id);
        if (!debateOpt.isPresent()) {
            throw new RuntimeException("辩论不存在");
        }

        Debate debate = debateOpt.get();

        // 检查权限
        if (!debate.getCreatorUsername().equals(username)) {
            throw new RuntimeException("只有创建者可以开始辩论");
        }

        // 检查状态
        if (debate.getStatus() != DebateStatus.WAITING) {
            throw new RuntimeException("只能开始等待中的辩论");
        }

        // 检查参与人数
        if (debate.getCurrentParticipants() < 2) {
            throw new RuntimeException("至少需要2人参与才能开始辩论");
        }

        debate.setStatus(DebateStatus.ACTIVE);
        debate.setStartedAt(LocalDateTime.now());
        debateRepository.save(debate);
    }

    /**
     * 结束辩论
     */
    public void endDebate(Long id, String username) {
        Optional<Debate> debateOpt = debateRepository.findById(id);
        if (!debateOpt.isPresent()) {
            throw new RuntimeException("辩论不存在");
        }

        Debate debate = debateOpt.get();

        // 检查权限
        if (!debate.getCreatorUsername().equals(username)) {
            throw new RuntimeException("只有创建者可以结束辩论");
        }

        // 检查状态
        if (debate.getStatus() != DebateStatus.ACTIVE) {
            throw new RuntimeException("只能结束进行中的辩论");
        }

        debate.setStatus(DebateStatus.COMPLETED);
        debate.setEndedAt(LocalDateTime.now());
        debateRepository.save(debate);
    }
}
