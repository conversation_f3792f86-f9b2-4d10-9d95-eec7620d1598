@echo off
chcp 65001 >nul
title AI辩论赛平台 - 前端启动

echo 🎯 AI辩论赛平台 - 前端智能启动
echo ================================

:: 检查Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Node.js，请先安装Node.js
    pause
    exit /b 1
)

:: 进入前端目录
if not exist "debate-tournament-frontend" (
    echo ❌ 错误: 未找到前端目录 debate-tournament-frontend
    pause
    exit /b 1
)

cd debate-tournament-frontend

:: 检查依赖
if not exist "node_modules" (
    echo 📦 正在安装依赖...
    npm install
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)

echo ================================
echo 🔍 正在检测后端服务端口...

:: 检测端口8081
netstat -an | find "8081" | find "LISTENING" >nul
if not errorlevel 1 (
    echo ✅ 检测到后端服务运行在端口: 8081
    set DETECTED_BACKEND_PORT=8081
    goto :start_frontend
)

:: 检测端口8080
netstat -an | find "8080" | find "LISTENING" >nul
if not errorlevel 1 (
    echo ✅ 检测到后端服务运行在端口: 8080
    set DETECTED_BACKEND_PORT=8080
    goto :start_frontend
)

:: 检测端口8082
netstat -an | find "8082" | find "LISTENING" >nul
if not errorlevel 1 (
    echo ✅ 检测到后端服务运行在端口: 8082
    set DETECTED_BACKEND_PORT=8082
    goto :start_frontend
)

:: 默认端口
echo ⚠️ 未检测到后端服务，使用默认端口 8081
set DETECTED_BACKEND_PORT=8081

:start_frontend
echo ================================
echo 🔗 后端服务: http://localhost:%DETECTED_BACKEND_PORT%/api
echo 🚀 启动前端开发服务器...
echo 💡 提示: 前端将通过代理转发请求到后端服务
echo ================================

:: 启动前端
npm run dev

pause
