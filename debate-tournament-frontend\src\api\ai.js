/**
 * AI 相关 API 接口
 */
import { request } from './index.js'

const AI_BASE = '/ai'

// AI 配置相关
export const aiConfigAPI = {
  // 获取AI配置列表
  getConfigs: (params = {}) => {
    return request.get(`${AI_BASE}/configs`, { params })
  },

  // 获取单个AI配置
  getConfig: (id) => {
    return request.get(`${AI_BASE}/configs/${id}`)
  },

  // 创建AI配置
  createConfig: (data) => {
    return request.post(`${AI_BASE}/configs`, data)
  },

  // 更新AI配置
  updateConfig: (id, data) => {
    return request.put(`${AI_BASE}/configs/${id}`, data)
  },

  // 删除AI配置
  deleteConfig: (id) => {
    return request.delete(`${AI_BASE}/configs/${id}`)
  },

  // 测试AI配置连接
  testConfig: (id) => {
    return request.post(`${AI_BASE}/configs/${id}/test`)
  },

  // 获取默认配置
  getDefaultConfig: () => {
    return request.get(`${AI_BASE}/configs/default`)
  },

  // 设置默认配置
  setDefaultConfig: (id) => {
    return request.put(`${AI_BASE}/configs/${id}/default`)
  },

  // 获取支持的AI提供商
  getProviders: () => {
    return request.get(`${AI_BASE}/configs/providers`)
  },

  // 获取指定提供商的模型列表
  getModels: (provider) => {
    return request.get(`${AI_BASE}/configs/providers/${provider}/models`)
  }
}

// AI 聊天相关
export const aiChatAPI = {
  // 发送聊天消息
  sendMessage: (data) => {
    return request.post(`${AI_BASE}/chat/send`, data)
  },

  // 流式聊天（SSE）
  sendStreamMessage: (data) => {
    return request.post(`${AI_BASE}/chat/stream`, data, {
      responseType: 'stream'
    })
  },

  // 获取用户聊天会话列表
  getUserSessions: () => {
    return request.get(`${AI_BASE}/chat/sessions`)
  },

  // 获取会话聊天记录
  getSessionMessages: (sessionId, params = {}) => {
    return request.get(`${AI_BASE}/chat/sessions/${sessionId}/messages`, { params })
  },

  // 删除聊天会话
  deleteSession: (sessionId) => {
    return request.delete(`${AI_BASE}/chat/sessions/${sessionId}`)
  },

  // 删除单条消息
  deleteMessage: (messageId) => {
    return request.delete(`${AI_BASE}/chat/messages/${messageId}`)
  },

  // 清空聊天历史
  clearHistory: () => {
    return request.delete(`${AI_BASE}/chat/history`)
  },

  // 获取聊天统计
  getChatStats: () => {
    return request.get(`${AI_BASE}/chat/stats`)
  }
}

// 多模态AI相关
export const multiModalAPI = {
  // 生成多模态响应
  generate: (data) => {
    return request.post(`${AI_BASE}/multimodal/generate`, data)
  },

  // 异步生成多模态响应
  generateAsync: (data) => {
    return request.post(`${AI_BASE}/multimodal/generate/async`, data)
  },

  // 仅生成语音响应
  generateAudio: (data) => {
    return request.post(`${AI_BASE}/multimodal/audio-only`, data)
  },

  // 获取用户响应列表
  getUserResponses: (params = {}) => {
    return request.get(`${AI_BASE}/multimodal/responses`, { params })
  },

  // 获取会话响应
  getSessionResponses: (sessionId) => {
    return request.get(`${AI_BASE}/multimodal/sessions/${sessionId}/responses`)
  },

  // 按类型获取响应
  getResponsesByType: (mediaType) => {
    return request.get(`${AI_BASE}/multimodal/responses/by-type`, {
      params: { mediaType }
    })
  },

  // 删除响应
  deleteResponse: (responseId) => {
    return request.delete(`${AI_BASE}/multimodal/responses/${responseId}`)
  },

  // 获取统计信息
  getStats: () => {
    return request.get(`${AI_BASE}/multimodal/stats`)
  }
}

// 音频相关
export const audioAPI = {
  // 文本转语音
  textToSpeech: (data) => {
    return request.post(`${AI_BASE}/audio/tts`, data, {
      responseType: 'blob'
    })
  },

  // 语音转文本
  speechToText: (audioFile) => {
    const formData = new FormData()
    formData.append('audio', audioFile)
    return request.post(`${AI_BASE}/audio/stt`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 获取支持的语音列表
  getVoices: () => {
    return request.get(`${AI_BASE}/audio/voices`)
  },

  // 获取音频配置
  getAudioConfig: () => {
    return request.get(`${AI_BASE}/audio/config`)
  },

  // 更新音频配置
  updateAudioConfig: (data) => {
    return request.put(`${AI_BASE}/audio/config`, data)
  }
}

// 思维导图相关
export const mindMapAPI = {
  // 生成思维导图
  generateMindMap: (data) => {
    return request.post(`${AI_BASE}/mindmap/generate`, data)
  },

  // 从会话生成思维导图
  generateFromSession: (sessionId, data) => {
    return request.post(`${AI_BASE}/mindmap/generate/session/${sessionId}`, data)
  },

  // 异步生成思维导图
  generateAsync: (data) => {
    return request.post(`${AI_BASE}/mindmap/generate/async`, data)
  },

  // 获取我的思维导图列表
  getMyMindMaps: (params = {}) => {
    return request.get(`${AI_BASE}/mindmap/my`, { params })
  },

  // 获取公开思维导图列表
  getPublicMindMaps: (params = {}) => {
    return request.get(`${AI_BASE}/mindmap/public`, { params })
  },

  // 搜索思维导图
  searchMindMaps: (params = {}) => {
    return request.get(`${AI_BASE}/mindmap/search`, { params })
  },

  // 获取思维导图详情
  getMindMapDetail: (id) => {
    return request.get(`${AI_BASE}/mindmap/${id}`)
  },

  // 更新思维导图
  updateMindMap: (id, data) => {
    return request.put(`${AI_BASE}/mindmap/${id}`, data)
  },

  // 删除思维导图
  deleteMindMap: (id) => {
    return request.delete(`${AI_BASE}/mindmap/${id}`)
  },

  // 批量删除思维导图
  batchDeleteMindMaps: (ids) => {
    return request.delete(`${AI_BASE}/mindmap/batch`, { data: ids })
  },

  // 复制思维导图
  copyMindMap: (id, options = {}) => {
    return request.post(`${AI_BASE}/mindmap/${id}/copy`, options)
  },

  // 获取统计信息
  getStats: () => {
    return request.get(`${AI_BASE}/mindmap/stats`)
  },

  // 导出为JSON格式
  exportAsJson: (id) => {
    return request.get(`${AI_BASE}/mindmap/${id}/export/json`, {
      responseType: 'blob'
    })
  },

  // 导出为Markdown格式
  exportAsMarkdown: (id) => {
    return request.get(`${AI_BASE}/mindmap/${id}/export/markdown`, {
      responseType: 'blob'
    })
  },

  // 导出为XML格式
  exportAsXml: (id) => {
    return request.get(`${AI_BASE}/mindmap/${id}/export/xml`, {
      responseType: 'blob'
    })
  },

  // 导出为文本格式
  exportAsText: (id) => {
    return request.get(`${AI_BASE}/mindmap/${id}/export/text`, {
      responseType: 'blob'
    })
  }
}

// 辩论AI相关
export const debateAiAPI = {
  // 开始AI辩论
  startDebate: (data) => {
    return request.post(`${AI_BASE}/debate/start`, data)
  },

  // 加入辩论
  joinDebate: (debateId) => {
    return request.post(`${AI_BASE}/debate/${debateId}/join`)
  },

  // 发送辩论消息
  sendDebateMessage: (debateId, data) => {
    return request.post(`${AI_BASE}/debate/${debateId}/message`, data)
  },

  // 获取辩论历史
  getDebateHistory: (debateId) => {
    return request.get(`${AI_BASE}/debate/${debateId}/history`)
  },

  // 结束辩论
  endDebate: (debateId) => {
    return request.post(`${AI_BASE}/debate/${debateId}/end`)
  },

  // 获取辩论结果
  getDebateResult: (debateId) => {
    return request.get(`${AI_BASE}/debate/${debateId}/result`)
  },

  // 评价辩论表现
  rateDebate: (debateId, data) => {
    return request.post(`${AI_BASE}/debate/${debateId}/rate`, data)
  }
}

// AI 工具集合
export const aiToolsAPI = {
  // 文本摘要
  summarize: (data) => {
    return request.post(`${AI_BASE}/tools/summarize`, data)
  },

  // 文本翻译
  translate: (data) => {
    return request.post(`${AI_BASE}/tools/translate`, data)
  },

  // 文本分析
  analyze: (data) => {
    return request.post(`${AI_BASE}/tools/analyze`, data)
  },

  // 关键词提取
  extractKeywords: (data) => {
    return request.post(`${AI_BASE}/tools/keywords`, data)
  },

  // 情感分析
  sentimentAnalysis: (data) => {
    return request.post(`${AI_BASE}/tools/sentiment`, data)
  },

  // 文本生成
  generate: (data) => {
    return request.post(`${AI_BASE}/tools/generate`, data)
  }
}

export default {
  aiConfigAPI,
  aiChatAPI,
  multiModalAPI,
  audioAPI,
  mindMapAPI,
  debateAiAPI,
  aiToolsAPI
}
