# AI 辩论赛平台应用配置文件
# 环境: 开发环境
# 版本: 1.0

server:
  port: 8081
  servlet:
    context-path: /api
    encoding:
      charset: UTF-8
      enabled: true
      force: true
      force-request: true
      force-response: true
  tomcat:
    uri-encoding: UTF-8

spring:
  application:
    name: ai-debate-tournament-platform

  # 允许循环依赖
  main:
    allow-circular-references: true
    banner-mode: "off"                            # 关闭Spring Boot启动横幅
    log-startup-info: false                       # 关闭启动信息日志

  # 数据源配置
  datasource:
    url: ***********************************************************************************************************************************************************
    username: root
    password: xiaoxiao123
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      # 连接池配置
      minimum-idle: 5
      maximum-pool-size: 20
      idle-timeout: 300000
      max-lifetime: 1200000
      connection-timeout: 20000
      pool-name: DatebookHikariCP
      connection-test-query: SELECT 1
      leak-detection-threshold: 60000
      auto-commit: true

  # JPA 配置
  jpa:
    hibernate:
      ddl-auto: none # 不自动创建表，使用我们的初始化脚本
      naming:
        physical-strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
    show-sql: false                               # 关闭SQL日志
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: false                         # 关闭SQL格式化
        use_sql_comments: false                   # 关闭SQL注释
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
        generate_statistics: false                # 关闭统计信息
        session:
          events:
            log:
              LOG_QUERIES_SLOWER_THAN_MS: 0       # 关闭慢查询日志
    open-in-view: false

  # 数据库初始化配置 - 禁用Spring Boot默认的初始化
  sql:
    init:
      mode: never # 禁用默认的schema.sql和data.sql

  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      password:
      database: 0
      timeout: 6000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB
      file-size-threshold: 2KB

  # 缓存配置
  cache:
    type: redis
    redis:
      time-to-live: 600000

  # 邮件配置（开发环境）
  mail:
    host: smtp.qq.com
    port: 587
    username: <EMAIL>
    password: your-auth-code
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
          ssl:
            trust: smtp.qq.com

  # 国际化配置
  messages:
    basename: messages
    encoding: UTF-8
    fallback-to-system-locale: false

  # Jackson JSON配置
  jackson:
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      write-dates-as-timestamps: false
    default-property-inclusion: non_null

  # 开发工具配置
  devtools:
    restart:
      enabled: true
    livereload:
      enabled: true

# 自定义应用配置
app:
  # 数据库初始化配置
  database:
    init:
      enabled: true                                    # 是否启用数据库自动初始化
      force: false                                     # 是否强制重新初始化（谨慎使用）
      script-path: db/init-database-merged.sql         # 初始化脚本路径
      check-on-startup: true                           # 启动时检查数据库状态

  # JWT配置
  jwt:
    secret: YourSecretKeyHere_ChangeThisInProduction_AtLeast256Bits_ForSecurity
    expiration: 86400000 # 24小时（毫秒）
    refresh-expiration: 604800000 # 7天（毫秒）

  # 安全配置
  security:
    cors:
      allowed-origins:
        - http://localhost:3000
        - http://localhost:5173
        - http://localhost:5174
        - http://localhost:5175
        - http://localhost:5176
        - http://localhost:8080
        - http://localhost:8081
        - http://127.0.0.1:3000
        - http://127.0.0.1:5173
        - http://127.0.0.1:5174
        - http://127.0.0.1:5175
        - http://127.0.0.1:5176
      allowed-methods:
        - GET
        - POST
        - PUT
        - DELETE
        - OPTIONS
      allowed-headers: "*"
      allow-credentials: true
      max-age: 3600

    # 验证码配置
    captcha:
      enabled: true
      length: 4
      width: 120
      height: 40
      expire-time: 300 # 5分钟（秒）

    # 密码策略配置
    password:
      min-length: 8
      require-uppercase: true
      require-lowercase: true
      require-numbers: true
      require-special-chars: false
      max-attempts: 5
      lockout-duration: 1800 # 30分钟（秒）

  # 文件存储配置
  file:
    storage:
      type: local # local, oss, s3
      base-path: uploads
      avatar:
        path: avatars
        max-size: 5MB
        allowed-types:
          - image/jpeg
          - image/png
          - image/gif
          - image/webp

  # 邮件配置（开发环境）
  mail:
    enabled: false # 开发环境下禁用邮件发送
    smtp:
      host: smtp.qq.com
      port: 587
      username: <EMAIL>
      password: your-auth-code
      from: <EMAIL>

  # 邮箱验证配置
  email:
    verification:
      expiration: 1440 # 24小时（分钟）
      resend-interval: 60 # 1分钟（秒）

  # 密码重置配置
  password:
    reset:
      expiration: 60 # 1小时（分钟）
      max-attempts: 3

  # 账号锁定配置
  account:
    lock:
      max-attempts: 5
      lock-duration: 30 # 30分钟

  # 文件上传配置
  upload:
    path: uploads
    avatar:
      max-size: 2097152 # 2MB
      allowed-types: jpg,jpeg,png,gif,webp

  # 文件清理配置
  cleanup:
    orphaned-files:
      enabled: true
      max-age-days: 7

  # 加密配置
  encryption:
    enabled: true
    key-store-path: keys
    public-key-file-name: public_key.pem
    private-key-file-name: private_key.pem
    key-id-file-name: key_id.txt
    rsa-key-size: 2048
    key-rotation-interval-hours: 24
    auto-generate-on-startup: true
    enable-key-rotation: false

  # AI模型配置
  ai:
    models:
      default: openai-gpt-3.5-turbo
      available:
        - name: openai-gpt-3.5-turbo
          display-name: GPT-3.5 Turbo
          provider: openai
          enabled: true
        - name: openai-gpt-4
          display-name: GPT-4
          provider: openai
          enabled: true
        - name: claude-3-sonnet
          display-name: Claude 3 Sonnet
          provider: anthropic
          enabled: true
        - name: claude-3-haiku
          display-name: Claude 3 Haiku
          provider: anthropic
          enabled: true

# 日志配置 - 开发环境（减少无关日志输出）
logging:
  level:
    root: ERROR                                    # 根日志级别设为ERROR，减少大量无关日志
    com.debate_ournament: INFO                     # 应用主要日志保持INFO级别
    com.debate_ournament.auth: INFO                # 认证相关日志
    com.debate_ournament.users: INFO               # 用户相关日志
    com.debate_ournament.security: WARN            # 安全相关日志

    # Spring框架日志 - 减少到最低
    org.springframework: ERROR                     # Spring框架日志设为ERROR
    org.springframework.boot: WARN                 # Spring Boot启动日志保持WARN
    org.springframework.boot.autoconfigure: ERROR # 自动配置日志设为ERROR
    org.springframework.security: ERROR           # Spring Security日志设为ERROR
    org.springframework.web: ERROR                # Web相关日志设为ERROR
    org.springframework.data: ERROR               # Data相关日志设为ERROR
    org.springframework.context: ERROR            # Context相关日志设为ERROR
    org.springframework.beans: ERROR              # Beans相关日志设为ERROR
    org.springframework.transaction: ERROR        # 事务相关日志设为ERROR

    # 数据库相关日志 - 减少到最低
    org.hibernate: ERROR                          # Hibernate日志设为ERROR
    org.hibernate.SQL: ERROR                     # SQL日志设为ERROR（生产环境应为ERROR）
    org.hibernate.type: ERROR                    # 类型日志设为ERROR
    org.hibernate.engine: ERROR                  # 引擎日志设为ERROR
    org.hibernate.event: ERROR                   # 事件日志设为ERROR
    org.hibernate.internal: ERROR                # 内部日志设为ERROR

    # 连接池日志
    com.zaxxer.hikari: ERROR                     # HikariCP日志设为ERROR
    com.zaxxer.hikari.HikariConfig: ERROR        # HikariCP配置日志设为ERROR

    # Tomcat相关日志
    org.apache.catalina: ERROR                   # Catalina日志设为ERROR
    org.apache.tomcat: ERROR                     # Tomcat日志设为ERROR
    org.apache.coyote: ERROR                     # Coyote日志设为ERROR

    # 其他第三方库日志
    io.lettuce: ERROR                            # Redis客户端日志设为ERROR
    org.redisson: ERROR                          # Redisson日志设为ERROR
    com.fasterxml.jackson: ERROR                 # Jackson日志设为ERROR
    org.thymeleaf: ERROR                         # Thymeleaf日志设为ERROR

    # Maven/构建相关警告
    org.apache.maven: ERROR                      # Maven日志设为ERROR

  pattern:
    console: "%d{HH:mm:ss.SSS} %-5level %logger{20} - %msg%n"  # 简化控制台输出格式
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/application.log
    max-size: 100MB
    max-history: 30
  charset:
    console: UTF-8
    file: UTF-8

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,env,database-init
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
  info:
    env:
      enabled: true
    build:
      enabled: true
  metrics:
    export:
      simple:
        enabled: true

# 自定义应用信息
info:
  app:
    name: AI辩论赛平台
    description: 基于人工智能的在线辩论比赛平台
    version: 1.0.0
    encoding: UTF-8
    java:
      version: 17
    spring-boot:
      version: 3.x
