@use 'sass:color';
@use 'sass:math';
@use '@/styles/variables' as v;
@use '@/styles/mixins' as m;

// 用户中心样式
.user-center {
  padding: v.$spacing-lg 0;
}

// 左侧用户信息
.user-profile {
  margin-bottom: v.$spacing-lg;

  &__header {
    text-align: center;
    padding: v.$spacing-lg;
    border-bottom: 1px solid color.adjust(black, $alpha: -0.9);
  }

  &__avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    margin: 0 auto v.$spacing-md;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  &__stats {
    display: flex;
    text-align: center;
    border-bottom: 1px solid color.adjust(black, $alpha: -0.9);

    .stat-item {
      flex: 1;
      padding: v.$spacing-md;

      .stat-value {
        display: block;
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: v.$spacing-xs;
      }

      .stat-label {
        color: color.adjust(black, $alpha: -0.4);
        font-size: 0.875rem;
      }
    }
  }
}

// 统计卡片
.statistics-card {
  background: white;
  border-radius: v.$shape-corner-medium;
  padding: v.$spacing-lg;
  box-shadow: 0 2px 4px color.adjust(black, $alpha: -0.9);

  &__title {
    font-size: 1.25rem;
    font-weight: 500;
    margin-bottom: v.$spacing-md;
    padding-bottom: v.$spacing-sm;
    border-bottom: 1px solid color.adjust(black, $alpha: -0.9);
  }

  &__content {
    display: grid;
    gap: v.$spacing-md;
  }
}

// 加载状态
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;

  .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid color.adjust(v.$color-primary, $alpha: -0.8);
    border-top-color: v.$color-primary;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// 页面布局
.page-header {
  margin-bottom: v.$spacing-lg;

  .page-title {
    font-size: 2rem;
    font-weight: 600;
    color: v.$color-text-primary;
    margin: 0;
  }
}

.profile-container {
  .row {
    display: flex;
    gap: v.$spacing-lg;

    .col-md-4 {
      flex: 0 0 300px;
    }

    .col-md-8 {
      flex: 1;
    }
  }
}

// 侧边导航
.side-nav {
  &__item {
    display: flex;
    align-items: center;
    padding: v.$spacing-md;
    cursor: pointer;
    border-bottom: 1px solid color.adjust(v.$color-border, $alpha: -0.5);
    transition: all 0.2s ease;

    &:hover {
      background-color: color.adjust(v.$color-primary, $alpha: -0.95);
    }

    &.active {
      background-color: color.adjust(v.$color-primary, $alpha: -0.9);
      color: v.$color-primary;
      border-right: 3px solid v.$color-primary;
    }

    &:last-child {
      border-bottom: none;
    }
  }

  &__icon {
    margin-right: v.$spacing-sm;
    font-size: 1.2rem;
  }

  &__text {
    font-weight: 500;
  }
}

// 内容区域
.content-section {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: v.$spacing-lg;
    padding-bottom: v.$spacing-md;
    border-bottom: 2px solid v.$color-border;

    .section-title {
      font-size: 1.5rem;
      font-weight: 600;
      color: v.$color-text-primary;
      margin: 0;
    }

    .section-actions {
      .form-select {
        min-width: 150px;
      }
    }
  }
}

// 辩论列表
.debate-list {
  display: grid;
  gap: v.$spacing-md;
}

.debate-item {
  padding: v.$spacing-lg;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid v.$color-border;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px color.adjust(v.$color-primary, $alpha: -0.85);
    border-color: v.$color-primary;
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: v.$spacing-md;
  }

  &__title {
    font-size: 1.1rem;
    font-weight: 600;
    color: v.$color-text-primary;
    margin: 0;
    flex: 1;
    margin-right: v.$spacing-md;
  }

  &__description {
    color: v.$color-text-secondary;
    margin-bottom: v.$spacing-md;
    line-height: 1.5;
  }

  &__meta {
    display: flex;
    gap: v.$spacing-md;
    margin-bottom: v.$spacing-md;

    .meta-item {
      display: flex;
      align-items: center;
      font-size: 0.9rem;
      color: v.$color-text-secondary;

      .meta-icon {
        margin-right: v.$spacing-xs;
      }
    }
  }

  &__footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
  }

  &__time {
    color: v.$color-text-secondary;
  }

  &__role {
    padding: 0.25rem 0.5rem;
    border-radius: v.$border-radius-sm;
    font-size: 0.8rem;
    font-weight: 500;

    &.role--supporter {
      background-color: color.adjust(v.$color-success, $alpha: -0.85);
      color: v.$color-success;
    }

    &.role--opposer {
      background-color: color.adjust(v.$color-error, $alpha: -0.85);
      color: v.$color-error;
    }

    &.role--judge {
      background-color: color.adjust(v.$color-warning, $alpha: -0.85);
      color: v.$color-warning;
    }

    &.role--spectator {
      background-color: color.adjust(v.$color-text-secondary, $alpha: -0.85);
      color: v.$color-text-secondary;
    }
  }
}

// 辩论状态
.debate-status {
  padding: 0.25rem 0.75rem;
  border-radius: v.$border-radius-lg;
  font-size: 0.8rem;
  font-weight: 500;

  &--active {
    background-color: color.adjust(v.$color-success, $alpha: -0.85);
    color: v.$color-success;
  }

  &--completed {
    background-color: color.adjust(v.$color-text-secondary, $alpha: -0.85);
    color: v.$color-text-secondary;
  }

  &--pending {
    background-color: color.adjust(v.$color-warning, $alpha: -0.85);
    color: v.$color-warning;
  }
}

// 统计图表
.statistics-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: v.$spacing-lg;
}

.win-rate-chart {
  .win-rate-indicator {
    text-align: center;
    margin-bottom: v.$spacing-lg;

    .win-rate-value {
      font-size: 2.5rem;
      font-weight: 700;
      color: v.$color-primary;
      margin-bottom: v.$spacing-sm;
    }

    .win-rate-bar {
      height: 8px;
      background-color: color.adjust(v.$color-border, $alpha: -0.5);
      border-radius: v.$border-radius-lg;
      overflow: hidden;

      .win-rate-fill {
        height: 100%;
        border-radius: v.$border-radius-lg;
        transition: width 0.3s ease;

        &--excellent {
          background: linear-gradient(90deg, #10b981, #059669);
        }

        &--good {
          background: linear-gradient(90deg, #3b82f6, #2563eb);
        }

        &--average {
          background: linear-gradient(90deg, #f59e0b, #d97706);
        }

        &--poor {
          background: linear-gradient(90deg, #ef4444, #dc2626);
        }
      }
    }
  }

  .win-rate-stats {
    display: flex;
    justify-content: space-around;

    .win-rate-stat {
      text-align: center;

      .stat-value {
        display: block;
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: v.$spacing-xs;

        &.win {
          color: v.$color-success;
        }

        &.draw {
          color: v.$color-warning;
        }

        &.loss {
          color: v.$color-error;
        }
      }

      .stat-label {
        font-size: 0.9rem;
        color: v.$color-text-secondary;
      }
    }
  }
}

.topic-preferences {
  .topic-preference {
    display: flex;
    align-items: center;
    margin-bottom: v.$spacing-md;

    .topic-name {
      flex: 0 0 80px;
      font-weight: 500;
      color: v.$color-text-primary;
    }

    .topic-bar {
      flex: 1;
      height: 20px;
      background-color: color.adjust(v.$color-border, $alpha: -0.5);
      border-radius: v.$border-radius-lg;
      margin: 0 v.$spacing-md;
      overflow: hidden;

      .topic-fill {
        height: 100%;
        border-radius: v.$border-radius-lg;
        transition: width 0.3s ease;

        &--high {
          background: linear-gradient(90deg, #8b5cf6, #7c3aed);
        }

        &--medium {
          background: linear-gradient(90deg, #06b6d4, #0891b2);
        }

        &--low {
          background: linear-gradient(90deg, #84cc16, #65a30d);
        }
      }
    }

    .topic-percentage {
      flex: 0 0 40px;
      text-align: right;
      font-weight: 500;
      color: v.$color-text-secondary;
    }
  }
}

// 设置相关样式
.settings-container {
  display: grid;
  gap: v.$spacing-lg;
}

.settings-section-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: v.$color-text-primary;
  margin: 0 0 v.$spacing-lg 0;
  padding-bottom: v.$spacing-sm;
  border-bottom: 1px solid v.$color-border;
}

.settings-form {
  max-width: 600px;
}

.settings-group {
  margin-bottom: v.$spacing-xl;

  &__title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: v.$spacing-md;
    color: v.$color-text-primary;
  }
}

.settings-option {
  display: flex;
  align-items: flex-start;
  margin-bottom: v.$spacing-lg;

  .switch {
    margin-right: v.$spacing-md;
    flex-shrink: 0;
  }

  .option-content {
    flex: 1;

    .option-title {
      display: block;
      font-weight: 500;
      color: v.$color-text-primary;
      margin-bottom: v.$spacing-xs;
    }

    .option-description {
      display: block;
      font-size: 0.9rem;
      color: v.$color-text-secondary;
      line-height: 1.4;
    }
  }
}

// 信息列表
.info-list {
  .info-item {
    display: flex;
    align-items: center;
    margin-bottom: v.$spacing-md;
    padding: v.$spacing-sm 0;
    border-bottom: 1px solid color.adjust(v.$color-border, $alpha: -0.5);

    &:last-child {
      border-bottom: none;
    }

    .info-label {
      flex: 0 0 140px;
      font-weight: 500;
      color: v.$color-text-secondary;
    }

    .info-value {
      flex: 1;
      color: v.$color-text-primary;
    }
  }
}

// 设备列表
.device-list {
  .device-item {
    display: flex;
    align-items: center;
    padding: v.$spacing-md;
    border: 1px solid v.$color-border;
    border-radius: v.$border-radius-md;
    margin-bottom: v.$spacing-md;

    &:last-child {
      margin-bottom: 0;
    }

    .device-icon {
      flex: 0 0 40px;
      font-size: 1.5rem;
      text-align: center;
    }

    .device-info {
      flex: 1;
      margin-left: v.$spacing-md;

      .device-name {
        font-weight: 500;
        color: v.$color-text-primary;
        margin-bottom: v.$spacing-xs;
      }

      .device-meta {
        font-size: 0.9rem;
        color: v.$color-text-secondary;
      }
    }
  }
}

// 加载和空状态
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: v.$color-text-secondary;

  .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid color.adjust(v.$color-primary, $alpha: -0.8);
    border-top-color: v.$color-primary;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: v.$spacing-md;
  }
}

.empty-state {
  text-align: center;
  padding: v.$spacing-xl;
  color: v.$color-text-secondary;

  p {
    margin-bottom: v.$spacing-lg;
    font-size: 1.1rem;
  }
}

.empty-chart {
  text-align: center;
  padding: v.$spacing-xl;
  color: v.$color-text-secondary;
  background-color: color.adjust(v.$color-surface-variant, $alpha: -0.5);
  border-radius: v.$border-radius-md;
}

// 模态框样式
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: color.adjust(black, $alpha: -0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: v.$border-radius-lg;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px color.adjust(black, $alpha: -0.9);

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: v.$spacing-lg;
    border-bottom: 1px solid v.$color-border;

    .modal__title {
      font-size: 1.25rem;
      font-weight: 600;
      color: v.$color-text-primary;
      margin: 0;
    }

    .modal__close {
      background: none;
      border: none;
      font-size: 1.5rem;
      cursor: pointer;
      color: v.$color-text-secondary;
      padding: 0;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;

      &:hover {
        background-color: color.adjust(v.$color-error, $alpha: -0.9);
        color: v.$color-error;
      }
    }
  }

  &__body {
    padding: v.$spacing-lg;
  }

  &__footer {
    display: flex;
    justify-content: flex-end;
    gap: v.$spacing-md;
    padding: v.$spacing-lg;
    border-top: 1px solid v.$color-border;
  }
}

// 头像上传
.avatar-upload {
  display: flex;
  align-items: center;
  gap: v.$spacing-md;

  .avatar-preview {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid v.$color-border;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .avatar-input {
    display: none;
  }
}

// 响应式布局
@include m.respond-to(lg) {
  .profile-container .row {
    .col-md-4 {
      flex: 0 0 280px;
    }
  }
}

@include m.respond-to(md) {
  .profile-container .row {
    flex-direction: column;

    .col-md-4,
    .col-md-8 {
      flex: none;
    }
  }

  .statistics-container {
    grid-template-columns: 1fr;
  }
}

@include m.respond-to(sm) {
  .user-profile {
    &__stats {
      flex-wrap: wrap;

      .stat-item {
        flex: 0 0 50%;
      }
    }
  }

  .debate-item {
    &__header {
      flex-direction: column;
      align-items: flex-start;

      .debate-item__title {
        margin-right: 0;
        margin-bottom: v.$spacing-sm;
      }
    }

    &__footer {
      flex-direction: column;
      align-items: flex-start;
      gap: v.$spacing-sm;
    }
  }

  .topic-preference {
    flex-direction: column;
    align-items: flex-start;

    .topic-name {
      margin-bottom: v.$spacing-xs;
    }

    .topic-bar {
      margin: 0 0 v.$spacing-xs 0;
    }
  }
}
