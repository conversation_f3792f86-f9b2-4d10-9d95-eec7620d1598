<template>
  <div class="debate-hall">
    <div class="container">
      <!-- 页面标题 -->
      <div class="debate-hall__header">
        <h1 class="debate-hall__title">辩论大厅</h1>
      </div>

      <!-- 筛选区域 -->
      <div class="debate-hall__filters">
        <div class="debate-hall__filter-group">
          <div class="filter-tag" v-for="category in categories" :key="category.id"
            :class="{ active: filters.category === category.id }" @click="setTopicFilter(category.id)">
            {{ category.name }}
          </div>
        </div>

        <div class="debate-hall__filter-group">
          <select v-model="filters.status" class="form-control">
            <option value="">全部状态</option>
            <option value="waiting">等待中</option>
            <option value="active">进行中</option>
            <option value="completed">已结束</option>
          </select>
        </div>

        <div class="debate-hall__search">
          <span class="search-icon">🔍</span>
          <input type="text" v-model="filters.search" placeholder="搜索辩题..." @input="handleSearchInput">
        </div>

        <button class="btn btn--primary debate-hall__create-btn" @click="openCreateModal">
          创建辩论
        </button>
      </div>

      <!-- 辩论列表 -->
      <div v-if="loading" class="text-center">
        <div class="spinner"></div>
        <p>加载中...</p>
      </div>

      <div v-else-if="paginatedDebates.length === 0" class="debate-hall__empty">
        <h3>暂无辩论</h3>
        <p>当前没有符合条件的辩论，您可以创建一个新的辩论</p>
        <button class="btn btn--primary" @click="openCreateModal">
          创建辩论
        </button>
      </div>

      <div v-else class="debate-hall__content">
        <debate-card v-for="debate in paginatedDebates" :key="debate.id" :debate="debate"
          @click="navigateToDebate(debate.id)" />
      </div>

      <!-- 分页 -->
      <div class="debate-hall__pagination" v-if="totalPages > 1">
        <button class="btn" :disabled="currentPage === 1" @click="changePage(currentPage - 1)">
          上一页
        </button>

        <button v-for="page in pageNumbers" :key="page" class="btn" :class="{ 'btn--primary': currentPage === page }"
          @click="changePage(page)">
          {{ page }}
        </button>

        <button class="btn" :disabled="currentPage === totalPages" @click="changePage(currentPage + 1)">
          下一页
        </button>
      </div>
    </div>

    <!-- 创建辩论对话框 -->
    <el-dialog v-model="createModalOpen" title="创建辩论" class="create-debate-modal" width="500px">

      <form class="create-debate-modal__form" @submit.prevent="handleCreateDebate">
        <div class="form-group">
          <label for="title" class="form-label">辩题</label>
          <input type="text" id="title" v-model="newDebate.title" class="form-control" placeholder="请输入辩题" required>
        </div>

        <div class="form-group">
          <label for="description" class="form-label">描述</label>
          <textarea id="description" v-model="newDebate.description" class="form-control" placeholder="请输入辩论描述"
            required></textarea>
        </div>

        <div class="form-group">
          <label for="category" class="form-label">分类</label>
          <select id="category" v-model="newDebate.category" class="form-control" required>
            <option value="" disabled>请选择分类</option>
            <option v-for="category in categories" :key="category.id" :value="category.id">
              {{ category.name }}
            </option>
          </select>
        </div>

        <div class="form-group">
          <label for="aiSettings" class="form-label">AI设置</label>
          <div class="ai-settings">
            <div class="form-check">
              <input type="checkbox" id="customizeAI" v-model="newDebate.customizeAI" class="form-check-input">
              <label for="customizeAI" class="form-check-label">
                自定义AI角色（否则使用系统默认AI）
              </label>
            </div>

            <div v-if="newDebate.customizeAI" class="ai-roles-settings">
              <p class="form-text">创建后可在辩论场景中选择具体AI模型</p>
            </div>
          </div>
        </div>
      </form>

      <template #footer>
        <el-button @click="createModalOpen = false">取消</el-button>
        <el-button type="primary" @click="handleCreateDebate" :loading="createLoading">
          创建
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script src="./js/DebateHall.js"></script>

<style lang="scss">
@use './scss/DebateHall.scss';
</style>
