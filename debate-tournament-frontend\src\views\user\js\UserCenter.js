import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/store/user';
import { useDebateStore } from '@/store/debate';
import { ElMessage } from 'element-plus';

export default {
  name: 'UserCenter',

  setup() {
    const router = useRouter();
    const userStore = useUserStore();
    const debateStore = useDebateStore();

    // 数据
    const activeTab = ref('debates');
    const debateFilter = ref('all');
    const showEditProfile = ref(false);

    const loadingDebates = ref(false);
    const loadingFavorites = ref(false);
    const loadingProfile = ref(false);
    const loadingPassword = ref(false);
    const loadingPrivacy = ref(false);
    const loadingPreferences = ref(false);
    const loadingNotifications = ref(false);
    const loadingSecurity = ref(false);

    const passwordSuccess = ref(false);
    const passwordMessage = ref('');
    const privacySuccess = ref(false);
    const privacyMessage = ref('');

    // 显示/隐藏密码
    const showCurrentPassword = ref(false);
    const showNewPassword = ref(false);
    const showConfirmPassword = ref(false);

    const user = reactive({
      username: '',
      email: '',
      bio: '',
      avatar: '',
      level: '',
      debateCount: 0,
      winCount: 0,
      pointsEarned: 0,
      lastLoginTime: '',
      registrationDate: ''
    });

    const profileForm = reactive({
      username: '',
      bio: '',
      avatar: null
    });

    const profileErrors = reactive({
      username: ''
    });

    const passwordForm = reactive({
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    });

    const passwordErrors = reactive({
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    });

    const privacySettings = reactive({
      showEmail: true,
      showStatistics: true,
      allowNotifications: true,
      showOnlineStatus: true,
      allowDirectMessages: true
    });

    const preferenceSettings = reactive({
      defaultDebateRole: 'spectator',
      preferredTopics: [],
      languagePreference: 'zh-CN',
      debateStyle: 'academic',
      aiModelPreference: 'gpt-4'
    });

    const notificationSettings = reactive({
      emailNotifications: true,
      debateInvitations: true,
      debateUpdates: true,
      mentionNotifications: true,
      systemAnnouncements: true,
      digestFrequency: 'daily'
    });

    const securitySettings = reactive({
      twoFactorEnabled: false,
      loginNotifications: true,
      trustedDevices: [],
      lastPasswordChange: null,
      sessionTimeout: 30
    });

    const userDebates = ref([]);
    const favorites = ref([]);
    const userData = reactive({
      totalDebates: 0,
      wins: 0,
      losses: 0,
      draws: 0,
      winRate: 0,
      topicPreferences: [],
      recentActivities: []
    });

    const tabs = [
      { id: 'debates', name: '我的辩论', icon: '📋' },
      { id: 'favorites', name: '收藏的辩论', icon: '⭐' },
      { id: 'statistics', name: '数据统计', icon: '📊' },
      { id: 'settings', name: '账户设置', icon: '⚙️' },
      { id: 'preferences', name: '偏好设置', icon: '🎯' },
      { id: 'notifications', name: '通知设置', icon: '🔔' },
      { id: 'privacy', name: '隐私设置', icon: '🔒' },
      { id: 'security', name: '账户安全', icon: '🛡️' }
    ];

    // 计算属性
    const filteredDebates = computed(() => {
      if (debateFilter.value === 'all') return userDebates.value;
      return userDebates.value.filter(debate => debate.status === debateFilter.value);
    });

    // 方法
    const fetchUserData = async () => {
      if (!userStore.isLoggedIn) {
        router.push('/login');
        return;
      }

      try {
        await userStore.getUserInfo();
        const userData = userStore.userInfo;

        Object.assign(user, {
          username: userData.username || '',
          email: userData.email || '',
          bio: userData.bio || '',
          avatar: userData.avatar || '',
          level: userData.level || 'Lv.1',
          debateCount: userData.debateCount || 0,
          winCount: userData.winCount || 0,
          pointsEarned: userData.pointsEarned || 0,
          lastLoginTime: userData.lastLoginTime || '',
          registrationDate: userData.createdAt || ''
        });

        Object.assign(profileForm, {
          username: userData.username || '',
          bio: userData.bio || '',
          avatar: userData.avatar || ''
        });

        // 使用默认值，因为这些设置可能不存在
        Object.assign(privacySettings, {
          showEmail: true,
          showStatistics: true,
          allowNotifications: true,
          showOnlineStatus: true,
          allowDirectMessages: true,
          ...userData.privacySettings
        });

        Object.assign(preferenceSettings, {
          defaultDebateRole: 'spectator',
          languagePreference: 'zh-CN',
          debateStyle: 'academic',
          aiModelPreference: 'gpt-4',
          ...userData.preferenceSettings
        });

        Object.assign(notificationSettings, {
          emailNotifications: true,
          debateInvitations: true,
          debateUpdates: true,
          mentionNotifications: true,
          systemAnnouncements: true,
          digestFrequency: 'daily',
          ...userData.notificationSettings
        });

        Object.assign(securitySettings, {
          twoFactorEnabled: false,
          loginNotifications: true,
          trustedDevices: [],
          lastPasswordChange: null,
          sessionTimeout: 30,
          ...userData.securitySettings
        });
      } catch (error) {
        console.error('Error fetching user data:', error);
        ElMessage.error('获取用户数据失败');
      }
    };

    const savePreferences = async () => {
      loadingPreferences.value = true;
      try {
        // 暂时使用updateUserInfo方法，后续可以添加专门的偏好设置API
        await userStore.updateUserInfo({ preferenceSettings });
        ElMessage.success('偏好设置已保存');
      } catch (error) {
        console.error('Error saving preferences:', error);
        ElMessage.error('保存偏好设置失败');
      } finally {
        loadingPreferences.value = false;
      }
    };

    const saveNotificationSettings = async () => {
      loadingNotifications.value = true;
      try {
        // 暂时使用updateUserInfo方法，后续可以添加专门的通知设置API
        await userStore.updateUserInfo({ notificationSettings });
        ElMessage.success('通知设置已保存');
      } catch (error) {
        console.error('Error saving notification settings:', error);
        ElMessage.error('保存通知设置失败');
      } finally {
        loadingNotifications.value = false;
      }
    };

    const toggleTwoFactorAuth = async () => {
      loadingSecurity.value = true;
      try {
        // 暂时只是切换状态，后续需要实现真正的两步验证API
        securitySettings.twoFactorEnabled = !securitySettings.twoFactorEnabled;
        await userStore.updateUserInfo({
          securitySettings: {
            ...securitySettings,
            twoFactorEnabled: securitySettings.twoFactorEnabled
          }
        });

        if (securitySettings.twoFactorEnabled) {
          ElMessage.success('已启用两步验证');
        } else {
          ElMessage.success('已关闭两步验证');
        }
      } catch (error) {
        console.error('Error toggling 2FA:', error);
        // 回滚状态
        securitySettings.twoFactorEnabled = !securitySettings.twoFactorEnabled;
        ElMessage.error('两步验证设置失败');
      } finally {
        loadingSecurity.value = false;
      }
    };

    const showTwoFactorSetupModal = (qrCode) => {
      // 实现二维码设置弹窗逻辑
    };

    const fetchUserDebates = async () => {
      loadingDebates.value = true;
      try {
        // 暂时使用模拟数据，后续需要实现真正的API
        userDebates.value = [
          {
            id: 1,
            title: '人工智能是否会取代人类工作',
            description: '讨论AI技术发展对就业市场的影响',
            status: 'completed',
            userRole: 'supporter',
            participantCount: 8,
            speechCount: 24,
            createTime: new Date().toISOString()
          },
          {
            id: 2,
            title: '远程工作是否比现场工作更有效率',
            description: '探讨不同工作模式的优劣',
            status: 'active',
            userRole: 'opposer',
            participantCount: 6,
            speechCount: 15,
            createTime: new Date(Date.now() - 86400000).toISOString()
          }
        ];
      } catch (error) {
        console.error('Error fetching user debates:', error);
      } finally {
        loadingDebates.value = false;
      }
    };

    const fetchUserFavorites = async () => {
      loadingFavorites.value = true;
      try {
        // 暂时使用模拟数据，后续需要实现真正的API
        favorites.value = [
          {
            id: 3,
            title: '环保与经济发展是否可以兼得',
            description: '探讨可持续发展的可能性',
            status: 'completed',
            userRole: 'spectator',
            participantCount: 12,
            speechCount: 36,
            createTime: new Date(Date.now() - 172800000).toISOString()
          }
        ];
      } catch (error) {
        console.error('Error fetching favorites:', error);
      } finally {
        loadingFavorites.value = false;
      }
    };

    const fetchUserStatistics = async () => {
      try {
        // 暂时使用模拟数据，后续需要实现真正的API
        const stats = {
          totalDebates: 15,
          wins: 8,
          losses: 5,
          draws: 2,
          winRate: 0.53,
          topicPreferences: [
            { name: '科技', percentage: 35 },
            { name: '社会', percentage: 28 },
            { name: '经济', percentage: 22 },
            { name: '环保', percentage: 15 }
          ]
        };

        Object.assign(userData, {
          totalDebates: stats.totalDebates || 0,
          wins: stats.wins || 0,
          losses: stats.losses || 0,
          draws: stats.draws || 0,
          winRate: stats.winRate || 0,
          topicPreferences: stats.topicPreferences || []
        });
      } catch (error) {
        console.error('Error fetching user statistics:', error);
      }
    };

    const navigateToDebate = (debateId) => {
      router.push(`/debate-arena/${debateId}`);
    };

    const handleAvatarChange = (event) => {
      const file = event.target.files[0];
      if (!file) return;

      // 检查文件类型和大小
      if (!file.type.match('image.*')) {
        alert('请选择图片文件');
        return;
      }

      if (file.size > 2 * 1024 * 1024) { // 2MB限制
        alert('图片大小不能超过2MB');
        return;
      }

      // 预览图片
      const reader = new FileReader();
      reader.onload = (e) => {
        profileForm.avatar = e.target.result;
      };
      reader.readAsDataURL(file);
    };

    const validateProfileForm = () => {
      let isValid = true;

      // 验证用户名
      if (!profileForm.username.trim()) {
        profileErrors.username = '用户名不能为空';
        isValid = false;
      } else if (profileForm.username.length < 3) {
        profileErrors.username = '用户名不能少于3个字符';
        isValid = false;
      } else {
        profileErrors.username = '';
      }

      return isValid;
    };

    const updateProfile = async () => {
      if (!validateProfileForm()) return;

      loadingProfile.value = true;
      try {
        await userStore.updateUserInfo({
          username: profileForm.username,
          bio: profileForm.bio,
          avatar: profileForm.avatar
        });

        // 更新成功后刷新用户数据
        await fetchUserData();

        // 关闭编辑模态框
        showEditProfile.value = false;
        ElMessage.success('个人资料更新成功');
      } catch (error) {
        console.error('Error updating profile:', error);
        profileErrors.username = error.message || '更新失败，请稍后再试';
      } finally {
        loadingProfile.value = false;
      }
    };

    const validatePasswordForm = () => {
      let isValid = true;

      // 验证当前密码
      if (!passwordForm.currentPassword) {
        passwordErrors.currentPassword = '请输入当前密码';
        isValid = false;
      } else {
        passwordErrors.currentPassword = '';
      }

      // 验证新密码
      if (!passwordForm.newPassword) {
        passwordErrors.newPassword = '请输入新密码';
        isValid = false;
      } else if (passwordForm.newPassword.length < 8) {
        passwordErrors.newPassword = '密码不能少于8个字符';
        isValid = false;
      } else {
        passwordErrors.newPassword = '';
      }

      // 验证确认密码
      if (passwordForm.newPassword !== passwordForm.confirmPassword) {
        passwordErrors.confirmPassword = '两次输入的密码不一致';
        isValid = false;
      } else {
        passwordErrors.confirmPassword = '';
      }

      return isValid;
    };

    const changePassword = async () => {
      if (!validatePasswordForm()) return;

      loadingPassword.value = true;
      try {
        await userStore.changePassword({
          currentPassword: passwordForm.currentPassword,
          newPassword: passwordForm.newPassword
        });

        // 重置表单
        passwordForm.currentPassword = '';
        passwordForm.newPassword = '';
        passwordForm.confirmPassword = '';

        passwordSuccess.value = true;
        passwordMessage.value = '密码修改成功';

        // 3秒后清除成功消息
        setTimeout(() => {
          passwordSuccess.value = false;
          passwordMessage.value = '';
        }, 3000);
      } catch (error) {
        console.error('Error changing password:', error);
        passwordSuccess.value = false;
        passwordMessage.value = error.message || '密码修改失败，请检查当前密码是否正确';
      } finally {
        loadingPassword.value = false;
      }
    };

    const savePrivacySettings = async () => {
      loadingPrivacy.value = true;
      try {
        await userStore.updateUserInfo({ privacySettings });

        privacySuccess.value = true;
        privacyMessage.value = '隐私设置已保存';

        // 3秒后清除成功消息
        setTimeout(() => {
          privacySuccess.value = false;
          privacyMessage.value = '';
        }, 3000);
      } catch (error) {
        console.error('Error saving privacy settings:', error);
        privacySuccess.value = false;
        privacyMessage.value = error.message || '保存失败，请稍后再试';
      } finally {
        loadingPrivacy.value = false;
      }
    };

    // 辅助方法
    const formatTime = (timestamp) => {
      if (!timestamp) return '';

      const date = new Date(timestamp);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    };

    const getStatusText = (status) => {
      const statusMap = {
        'waiting': '等待中',
        'active': '进行中',
        'paused': '已暂停',
        'completed': '已结束'
      };
      return statusMap[status] || '未知状态';
    };

    const getRoleText = (role) => {
      const roleMap = {
        'supporter': '正方',
        'opposer': '反方',
        'judge': '裁判',
        'spectator': '观众'
      };
      return roleMap[role] || '未知角色';
    };

    // 获取胜率样式类
    const getWinRateClass = (winRate) => {
      if (winRate >= 0.7) return 'win-rate-fill--excellent';
      if (winRate >= 0.5) return 'win-rate-fill--good';
      if (winRate >= 0.3) return 'win-rate-fill--average';
      return 'win-rate-fill--poor';
    };

    // 获取主题偏好样式类
    const getTopicClass = (percentage) => {
      if (percentage >= 30) return 'topic-fill--high';
      if (percentage >= 20) return 'topic-fill--medium';
      return 'topic-fill--low';
    };

    // 移除设备
    const revokeDevice = async (deviceId) => {
      try {
        // 这里应该调用API移除设备
        securitySettings.trustedDevices = securitySettings.trustedDevices.filter(
          device => device.id !== deviceId
        );
        ElMessage.success('设备已移除');
      } catch (error) {
        console.error('Error revoking device:', error);
        ElMessage.error('移除设备失败');
      }
    };

    // 监听标签切换以按需加载数据
    const handleTabChange = async (tab) => {
      activeTab.value = tab;

      if (tab === 'debates' && userDebates.value.length === 0) {
        await fetchUserDebates();
      } else if (tab === 'favorites' && favorites.value.length === 0) {
        await fetchUserFavorites();
      } else if (tab === 'statistics') {
        await fetchUserStatistics();
      }
    };

    // 生命周期钩子
    onMounted(async () => {
      if (!userStore.isLoggedIn) {
        router.push('/login');
        return;
      }

      await fetchUserData();
      await fetchUserDebates(); // 默认加载我的辩论
    });

    return {
      user,
      activeTab,
      debateFilter,
      showEditProfile,
      loadingDebates,
      loadingFavorites,
      loadingProfile,
      loadingPassword,
      loadingPrivacy,
      loadingPreferences,
      loadingNotifications,
      loadingSecurity,
      passwordSuccess,
      passwordMessage,
      privacySuccess,
      privacyMessage,
      showCurrentPassword,
      showNewPassword,
      showConfirmPassword,
      profileForm,
      profileErrors,
      passwordForm,
      passwordErrors,
      privacySettings,
      preferenceSettings,
      notificationSettings,
      securitySettings,
      userDebates,
      favorites,
      userData,
      tabs,
      filteredDebates,
      navigateToDebate,
      handleAvatarChange,
      updateProfile,
      changePassword,
      savePrivacySettings,
      savePreferences,
      saveNotificationSettings,
      toggleTwoFactorAuth,
      revokeDevice,
      formatTime,
      getStatusText,
      getRoleText,
      getWinRateClass,
      getTopicClass,
      handleTabChange
    };
  }
};
