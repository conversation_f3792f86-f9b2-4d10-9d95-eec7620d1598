package com.debate_ournament.users.controller;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.debate_ournament.dto.common.ApiResponse;
import com.debate_ournament.service.KeyManagementService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * 密钥管理控制器
 *
 * 提供密钥生成、轮换和管理功能
 * 仅管理员可访问
 *
 * <AUTHOR> Debate Tournament Platform
 * @version 1.0
 * @since 2024
 */
@RestController
@RequestMapping("/admin/key-management")
@Tag(name = "密钥管理", description = "RSA密钥对生成、轮换和管理功能（管理员专用）")
@SecurityRequirement(name = "bearerAuth")
public class KeyManagementController {

    private static final Logger logger = LoggerFactory.getLogger(KeyManagementController.class);

    private final KeyManagementService keyManagementService;

    @Autowired
    public KeyManagementController(KeyManagementService keyManagementService) {
        this.keyManagementService = keyManagementService;
    }

    /**
     * 获取当前密钥信息
     *
     * @return 密钥状态和信息
     */
    @GetMapping("/info")
    @Operation(summary = "获取密钥信息", description = "获取当前RSA密钥对的状态和基本信息")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getKeyInfo() {
        try {
            logger.debug("管理员请求获取密钥信息");

            Map<String, Object> keyInfo = new HashMap<>();
            keyInfo.put("keyId", keyManagementService.getCurrentKeyId());
            keyInfo.put("keyGeneratedTime", keyManagementService.getKeyGeneratedTime());
            keyInfo.put("keyRotationNeeded", keyManagementService.isKeyRotationNeeded());
            keyInfo.put("keyStorePath", keyManagementService.getKeyStorePath().toString());

            // 公钥信息（不包含私钥）
            keyInfo.put("publicKeyBase64", keyManagementService.getCurrentPublicKeyBase64());

            logger.info("返回密钥信息，密钥ID: {}", keyManagementService.getCurrentKeyId());
            return ResponseEntity.ok(ApiResponse.success("获取密钥信息成功", keyInfo));

        } catch (Exception e) {
            logger.error("获取密钥信息失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取密钥信息失败"));
        }
    }

    /**
     * 生成新的密钥对
     *
     * @return 新密钥对信息
     */
    @PostMapping("/generate")
    @Operation(summary = "生成新密钥对", description = "手动生成新的RSA密钥对，旧密钥将被备份")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> generateNewKeyPair() {
        try {
            logger.info("管理员请求生成新密钥对");

            String oldKeyId = keyManagementService.getCurrentKeyId();
            String newKeyId = keyManagementService.generateNewKeyPair();

            Map<String, Object> result = new HashMap<>();
            result.put("oldKeyId", oldKeyId);
            result.put("newKeyId", newKeyId);
            result.put("keyGeneratedTime", keyManagementService.getKeyGeneratedTime());
            result.put("publicKeyBase64", keyManagementService.getCurrentPublicKeyBase64());

            logger.info("成功生成新密钥对，旧密钥ID: {}, 新密钥ID: {}", oldKeyId, newKeyId);
            return ResponseEntity.ok(ApiResponse.success("密钥对生成成功", result));

        } catch (Exception e) {
            logger.error("生成新密钥对失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("生成密钥对失败: " + e.getMessage()));
        }
    }

    /**
     * 检查密钥轮换状态
     *
     * @return 轮换检查结果
     */
    @GetMapping("/rotation-check")
    @Operation(summary = "检查密钥轮换", description = "检查当前密钥是否需要进行轮换")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> checkKeyRotation() {
        try {
            logger.debug("管理员请求检查密钥轮换状态");

            boolean rotationNeeded = keyManagementService.isKeyRotationNeeded();

            Map<String, Object> rotationInfo = new HashMap<>();
            rotationInfo.put("rotationNeeded", rotationNeeded);
            rotationInfo.put("currentKeyId", keyManagementService.getCurrentKeyId());
            rotationInfo.put("keyGeneratedTime", keyManagementService.getKeyGeneratedTime());

            if (rotationNeeded) {
                rotationInfo.put("message", "建议进行密钥轮换");
                rotationInfo.put("recommendation", "ROTATE_NOW");
            } else {
                rotationInfo.put("message", "当前密钥状态良好");
                rotationInfo.put("recommendation", "NO_ACTION_NEEDED");
            }

            logger.debug("密钥轮换检查完成，需要轮换: {}", rotationNeeded);
            return ResponseEntity.ok(ApiResponse.success("轮换检查完成", rotationInfo));

        } catch (Exception e) {
            logger.error("检查密钥轮换失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("轮换检查失败"));
        }
    }

    /**
     * 获取密钥存储路径
     *
     * @return 存储路径信息
     */
    @GetMapping("/storage-info")
    @Operation(summary = "获取存储信息", description = "获取密钥文件的存储路径和状态")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getStorageInfo() {
        try {
            logger.debug("管理员请求获取密钥存储信息");

            Map<String, Object> storageInfo = new HashMap<>();
            storageInfo.put("keyStorePath", keyManagementService.getKeyStorePath().toString());
            storageInfo.put("absolutePath", keyManagementService.getKeyStorePath().toAbsolutePath().toString());
            storageInfo.put("exists", keyManagementService.getKeyStorePath().toFile().exists());
            storageInfo.put("readable", keyManagementService.getKeyStorePath().toFile().canRead());
            storageInfo.put("writable", keyManagementService.getKeyStorePath().toFile().canWrite());

            // 检查密钥文件是否存在
            try {
                storageInfo.put("hasValidKeys", keyManagementService.getCurrentKeyId() != null);
            } catch (Exception e) {
                storageInfo.put("hasValidKeys", false);
                storageInfo.put("keyError", e.getMessage());
            }

            logger.debug("返回密钥存储信息");
            return ResponseEntity.ok(ApiResponse.success("获取存储信息成功", storageInfo));

        } catch (Exception e) {
            logger.error("获取密钥存储信息失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("获取存储信息失败"));
        }
    }

    /**
     * 获取密钥系统健康状态
     *
     * @return 系统健康检查结果
     */
    @GetMapping("/health")
    @Operation(summary = "健康检查", description = "检查密钥管理系统的整体健康状态")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSystemHealth() {
        try {
            logger.debug("管理员请求密钥系统健康检查");

            Map<String, Object> healthInfo = new HashMap<>();
            boolean isHealthy = true;
            StringBuilder issues = new StringBuilder();

            // 检查密钥是否存在
            try {
                String keyId = keyManagementService.getCurrentKeyId();
                healthInfo.put("keyExists", keyId != null);
                healthInfo.put("currentKeyId", keyId);

                if (keyId == null) {
                    isHealthy = false;
                    issues.append("密钥不存在; ");
                }
            } catch (Exception e) {
                healthInfo.put("keyExists", false);
                healthInfo.put("keyError", e.getMessage());
                isHealthy = false;
                issues.append("密钥访问错误: ").append(e.getMessage()).append("; ");
            }

            // 检查存储路径
            try {
                boolean pathExists = keyManagementService.getKeyStorePath().toFile().exists();
                healthInfo.put("storagePathExists", pathExists);

                if (!pathExists) {
                    isHealthy = false;
                    issues.append("存储路径不存在; ");
                }
            } catch (Exception e) {
                healthInfo.put("storagePathExists", false);
                isHealthy = false;
                issues.append("存储路径检查失败; ");
            }

            // 检查密钥轮换状态
            try {
                boolean rotationNeeded = keyManagementService.isKeyRotationNeeded();
                healthInfo.put("rotationNeeded", rotationNeeded);

                if (rotationNeeded) {
                    issues.append("建议进行密钥轮换; ");
                }
            } catch (Exception e) {
                healthInfo.put("rotationCheckFailed", true);
                issues.append("轮换检查失败; ");
            }

            // 设置整体健康状态
            healthInfo.put("healthy", isHealthy);
            healthInfo.put("status", isHealthy ? "HEALTHY" : "UNHEALTHY");

            if (issues.length() > 0) {
                healthInfo.put("issues", issues.toString());
            }

            healthInfo.put("checkTime", System.currentTimeMillis());

            logger.info("密钥系统健康检查完成，状态: {}", isHealthy ? "健康" : "不健康");

            if (isHealthy) {
                return ResponseEntity.ok(ApiResponse.success("系统健康", healthInfo));
            } else {
                return ResponseEntity.status(503) // Service Unavailable
                        .body(ApiResponse.success("系统存在问题", healthInfo));
            }

        } catch (Exception e) {
            logger.error("密钥系统健康检查失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("健康检查失败"));
        }
    }
}
