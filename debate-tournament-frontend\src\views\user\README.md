# 个人中心页面

## 概述

个人中心页面是用户管理个人信息、查看辩论记录、配置偏好设置的核心页面。页面采用响应式设计，支持多种设备访问。

## 功能模块

### 1. 用户信息展示
- **头像显示**: 支持自定义头像上传
- **基本信息**: 用户名、邮箱、个人简介
- **等级系统**: 显示用户辩论等级
- **统计数据**: 参与辩论数、获胜场次、积分等

### 2. 我的辩论
- **辩论列表**: 显示用户参与的所有辩论
- **状态筛选**: 支持按辩论状态筛选（全部/进行中/已结束）
- **角色标识**: 显示用户在每场辩论中的角色
- **快速跳转**: 点击辩论卡片可直接进入辩论页面

### 3. 收藏的辩论
- **收藏管理**: 查看和管理收藏的辩论
- **快速访问**: 便于用户快速找到感兴趣的辩论

### 4. 数据统计
- **胜率分析**: 可视化显示辩论胜率
- **主题偏好**: 展示用户最常参与的辩论主题
- **趋势图表**: 使用渐变色彩区分不同等级的数据

### 5. 账户设置
- **密码修改**: 安全的密码更改功能
- **个人资料编辑**: 修改用户名、简介、头像等

### 6. 偏好设置
- **辩论偏好**: 默认角色、辩论风格选择
- **语言设置**: 界面语言偏好
- **AI模型**: 选择偏好的AI辩论对手

### 7. 通知设置
- **邮件通知**: 控制各类邮件通知的开关
- **通知频率**: 设置通知摘要的发送频率
- **细粒度控制**: 分别控制不同类型的通知

### 8. 隐私设置
- **信息可见性**: 控制个人信息的公开程度
- **互动权限**: 设置私信、通知等权限

### 9. 账户安全
- **两步验证**: 增强账户安全性
- **登录安全**: 异常登录检测和通知
- **设备管理**: 查看和管理已登录设备
- **会话控制**: 设置会话超时时间

## 技术特性

### 1. 响应式设计
- **移动端适配**: 完全支持手机和平板设备
- **弹性布局**: 使用Flexbox和Grid布局
- **断点设计**: 针对不同屏幕尺寸优化

### 2. 无内联样式
- **外部样式**: 所有样式都在SCSS文件中定义
- **模块化**: 样式按功能模块组织
- **可维护性**: 便于样式的统一管理和修改

### 3. 组件化架构
- **Vue 3 Composition API**: 使用最新的Vue 3语法
- **逻辑分离**: JS逻辑独立文件管理
- **可复用性**: 组件设计考虑复用性

### 4. 状态管理
- **Pinia Store**: 使用Pinia进行状态管理
- **响应式数据**: 数据变化自动更新UI
- **持久化**: 重要数据本地存储

### 5. 用户体验优化
- **加载状态**: 所有异步操作都有加载提示
- **错误处理**: 完善的错误提示和处理
- **防抖优化**: 避免频繁的API调用
- **动画效果**: 平滑的过渡动画

## 文件结构

```
src/views/user/
├── UserCenter.vue          # 主组件文件
├── js/
│   └── UserCenter.js       # 逻辑处理文件
├── scss/
│   └── UserCenter.scss     # 样式文件
└── README.md              # 说明文档
```

## 样式系统

### 1. 设计令牌
- 使用SCSS变量统一管理颜色、间距、字体等
- 支持主题切换的设计系统
- 语义化的颜色命名

### 2. 组件样式
- BEM命名规范
- 模块化的样式组织
- 响应式断点管理

### 3. 动画效果
- CSS过渡动画
- 悬停效果
- 加载动画

## 数据流

### 1. 初始化
1. 检查用户登录状态
2. 获取用户基本信息
3. 加载默认标签页数据

### 2. 标签切换
1. 按需加载数据
2. 缓存已加载的数据
3. 显示加载状态

### 3. 设置保存
1. 表单验证
2. API调用
3. 状态更新
4. 用户反馈

## 开发指南

### 1. 添加新功能
1. 在Vue模板中添加UI结构
2. 在JS文件中添加逻辑处理
3. 在SCSS文件中添加样式
4. 更新return语句导出新的方法

### 2. 样式开发
1. 遵循BEM命名规范
2. 使用SCSS变量和混入
3. 考虑响应式设计
4. 避免内联样式

### 3. 逻辑开发
1. 使用Composition API
2. 合理组织响应式数据
3. 添加错误处理
4. 考虑性能优化

## 注意事项

1. **安全性**: 敏感操作需要二次确认
2. **性能**: 大数据量时考虑分页加载
3. **兼容性**: 确保在不同浏览器中正常工作
4. **可访问性**: 支持键盘导航和屏幕阅读器
