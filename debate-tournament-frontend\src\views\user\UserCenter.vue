<template>
  <div class="user-center">
    <div class="container">
      <div class="page-header">
        <h1 class="page-title">个人中心</h1>
      </div>

      <div class="profile-container">
        <div class="row">
          <!-- 左侧用户信息 -->
          <div class="col-md-4">
            <div class="card user-profile">
              <div class="user-profile__header">
                <div class="user-profile__avatar">
                  <img :src="user.avatar || 'http://placehold.co/150'" :alt="user.username">
                </div>
                <h2 class="user-profile__name">{{ user.username }}</h2>
                <p class="user-profile__email">{{ user.email }}</p>
                <div class="user-profile__level">
                  <span class="level-label">辩论等级</span>
                  <span class="level-badge">{{ user.level || 'Lv.1' }}</span>
                </div>
              </div>

              <div class="user-profile__stats">
                <div class="stat-item">
                  <span class="stat-value">{{ user.debateCount || 0 }}</span>
                  <span class="stat-label">参与辩论</span>
                </div>
                <div class="stat-item">
                  <span class="stat-value">{{ user.winCount || 0 }}</span>
                  <span class="stat-label">获胜场次</span>
                </div>
                <div class="stat-item">
                  <span class="stat-value">{{ user.pointsEarned || 0 }}</span>
                  <span class="stat-label">积分</span>
                </div>
              </div>

              <div class="user-profile__actions">
                <button class="btn btn--primary" @click="showEditProfile = true">
                  编辑资料
                </button>
              </div>
            </div>

            <div class="card side-nav">
              <div class="side-nav__item" v-for="tab in tabs" :key="tab.id" :class="{ 'active': activeTab === tab.id }"
                @click="activeTab = tab.id">
                <span class="side-nav__icon">{{ tab.icon }}</span>
                <span class="side-nav__text">{{ tab.name }}</span>
              </div>
            </div>
          </div>

          <!-- 右侧内容 -->
          <div class="col-md-8">
            <!-- 我的辩论 -->
            <div v-if="activeTab === 'debates'" class="content-section">
              <div class="section-header">
                <h2 class="section-title">我的辩论</h2>
                <div class="section-actions">
                  <select v-model="debateFilter" class="form-select">
                    <option value="all">全部辩论</option>
                    <option value="active">进行中</option>
                    <option value="completed">已结束</option>
                  </select>
                </div>
              </div>

              <div class="debate-list" v-if="!loadingDebates && userDebates.length > 0">
                <div v-for="debate in userDebates" :key="debate.id" class="debate-item card"
                  @click="navigateToDebate(debate.id)">
                  <div class="debate-item__header">
                    <h3 class="debate-item__title">{{ debate.title }}</h3>
                    <span class="debate-status" :class="`debate-status--${debate.status}`">
                      {{ getStatusText(debate.status) }}
                    </span>
                  </div>

                  <div class="debate-item__content">
                    <p class="debate-item__description">{{ debate.description }}</p>
                    <div class="debate-item__meta">
                      <span class="meta-item">
                        <span class="meta-icon">👥</span>
                        {{ debate.participantCount || 0 }} 参与者
                      </span>
                      <span class="meta-item">
                        <span class="meta-icon">💬</span>
                        {{ debate.speechCount || 0 }} 发言
                      </span>
                    </div>
                  </div>

                  <div class="debate-item__footer">
                    <span class="debate-item__time">{{ formatTime(debate.createTime) }}</span>
                    <span class="debate-item__role" :class="`role--${debate.userRole}`">
                      {{ getRoleText(debate.userRole) }}
                    </span>
                  </div>
                </div>
              </div>

              <div v-else-if="loadingDebates" class="loading-container">
                <div class="spinner"></div>
                <p>加载中...</p>
              </div>

              <div v-else class="empty-state">
                <p>您还没有参与任何辩论</p>
                <router-link to="/debate-hall" class="btn btn--primary">
                  前往辩论大厅
                </router-link>
              </div>
            </div>

            <!-- 收藏的辩论 -->
            <div v-if="activeTab === 'favorites'" class="content-section">
              <div class="section-header">
                <h2 class="section-title">收藏的辩论</h2>
              </div>

              <div v-if="!loadingFavorites && favorites.length > 0" class="debate-list">
                <div v-for="debate in favorites" :key="debate.id" class="debate-item card"
                  @click="navigateToDebate(debate.id)">
                  <!-- 与上面类似的内容 -->
                </div>
              </div>

              <div v-else-if="loadingFavorites" class="loading-container">
                <div class="spinner"></div>
                <p>加载中...</p>
              </div>

              <div v-else class="empty-state">
                <p>您还没有收藏任何辩论</p>
                <router-link to="/debate-hall" class="btn btn--primary">
                  前往辩论大厅
                </router-link>
              </div>
            </div>

            <!-- 数据统计 -->
            <div v-if="activeTab === 'statistics'" class="content-section">
              <div class="section-header">
                <h2 class="section-title">数据统计</h2>
              </div>

              <div class="statistics-container">
                <div class="card statistics-card">
                  <h3 class="statistics-card__title">辩论胜率</h3>
                  <div class="win-rate-chart" v-if="userData.totalDebates > 0">
                    <div class="win-rate-indicator">
                      <div class="win-rate-value">{{ Math.round(userData.winRate * 100) }}%</div>
                      <div class="win-rate-bar">
                        <div class="win-rate-fill" :class="getWinRateClass(userData.winRate)"></div>
                      </div>
                    </div>
                    <div class="win-rate-stats">
                      <div class="win-rate-stat">
                        <span class="stat-value win">{{ userData.wins }}</span>
                        <span class="stat-label">胜利</span>
                      </div>
                      <div class="win-rate-stat">
                        <span class="stat-value draw">{{ userData.draws }}</span>
                        <span class="stat-label">平局</span>
                      </div>
                      <div class="win-rate-stat">
                        <span class="stat-value loss">{{ userData.losses }}</span>
                        <span class="stat-label">失败</span>
                      </div>
                    </div>
                  </div>
                  <div class="empty-chart" v-else>
                    <p>暂无数据</p>
                  </div>
                </div>

                <div class="card statistics-card">
                  <h3 class="statistics-card__title">辩论主题偏好</h3>
                  <div class="topic-preferences" v-if="userData.topicPreferences.length > 0">
                    <div v-for="topic in userData.topicPreferences" :key="topic.name" class="topic-preference">
                      <span class="topic-name">{{ topic.name }}</span>
                      <div class="topic-bar">
                        <div class="topic-fill" :class="getTopicClass(topic.percentage)"></div>
                      </div>
                      <span class="topic-percentage">{{ topic.percentage }}%</span>
                    </div>
                  </div>
                  <div class="empty-chart" v-else>
                    <p>暂无数据</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 账户设置 -->
            <div v-if="activeTab === 'settings'" class="content-section">
              <div class="section-header">
                <h2 class="section-title">账户设置</h2>
              </div>

              <div class="settings-container">
                <div class="card">
                  <h3 class="settings-section-title">修改密码</h3>
                  <form @submit.prevent="changePassword" class="form">
                    <div class="form-group">
                      <label class="form-label" for="currentPassword">当前密码</label>
                      <div class="password-input">
                        <input :type="showCurrentPassword ? 'text' : 'password'" id="currentPassword"
                          class="form-control" v-model="passwordForm.currentPassword"
                          :class="{ 'is-invalid': passwordErrors.currentPassword }" required />
                        <button type="button" class="toggle-password-btn"
                          @click="showCurrentPassword = !showCurrentPassword">
                          {{ showCurrentPassword ? '隐藏' : '显示' }}
                        </button>
                      </div>
                      <span v-if="passwordErrors.currentPassword" class="form-text text-error">
                        {{ passwordErrors.currentPassword }}
                      </span>
                    </div>

                    <div class="form-group">
                      <label class="form-label" for="newPassword">新密码</label>
                      <div class="password-input">
                        <input :type="showNewPassword ? 'text' : 'password'" id="newPassword" class="form-control"
                          v-model="passwordForm.newPassword" :class="{ 'is-invalid': passwordErrors.newPassword }"
                          required />
                        <button type="button" class="toggle-password-btn" @click="showNewPassword = !showNewPassword">
                          {{ showNewPassword ? '隐藏' : '显示' }}
                        </button>
                      </div>
                      <span v-if="passwordErrors.newPassword" class="form-text text-error">
                        {{ passwordErrors.newPassword }}
                      </span>
                    </div>

                    <div class="form-group">
                      <label class="form-label" for="confirmPassword">确认新密码</label>
                      <div class="password-input">
                        <input :type="showConfirmPassword ? 'text' : 'password'" id="confirmPassword"
                          class="form-control" v-model="passwordForm.confirmPassword"
                          :class="{ 'is-invalid': passwordErrors.confirmPassword }" required />
                        <button type="button" class="toggle-password-btn"
                          @click="showConfirmPassword = !showConfirmPassword">
                          {{ showConfirmPassword ? '隐藏' : '显示' }}
                        </button>
                      </div>
                      <span v-if="passwordErrors.confirmPassword" class="form-text text-error">
                        {{ passwordErrors.confirmPassword }}
                      </span>
                    </div>

                    <button type="submit" class="btn btn--primary" :disabled="loadingPassword">
                      {{ loadingPassword ? '提交中...' : '修改密码' }}
                    </button>

                    <div v-if="passwordMessage" class="form-message" :class="passwordSuccess ? 'success' : 'error'">
                      {{ passwordMessage }}
                    </div>
                  </form>
                </div>

                <div class="card">
                  <h3 class="settings-section-title">隐私设置</h3>
                  <div class="settings-options">
                    <div class="settings-option">
                      <label class="switch">
                        <input type="checkbox" v-model="privacySettings.showEmail">
                        <span class="slider"></span>
                      </label>
                      <span>显示我的电子邮箱</span>
                    </div>

                    <div class="settings-option">
                      <label class="switch">
                        <input type="checkbox" v-model="privacySettings.showStatistics">
                        <span class="slider"></span>
                      </label>
                      <span>显示我的辩论统计数据</span>
                    </div>

                    <div class="settings-option">
                      <label class="switch">
                        <input type="checkbox" v-model="privacySettings.allowNotifications">
                        <span class="slider"></span>
                      </label>
                      <span>接收电子邮件通知</span>
                    </div>

                    <button class="btn btn--primary mt-3" @click="savePrivacySettings" :disabled="loadingPrivacy">
                      {{ loadingPrivacy ? '保存中...' : '保存设置' }}
                    </button>

                    <div v-if="privacyMessage" class="form-message" :class="privacySuccess ? 'success' : 'error'">
                      {{ privacyMessage }}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 偏好设置 -->
            <div v-if="activeTab === 'preferences'" class="content-section">
              <div class="section-header">
                <h2 class="section-title">偏好设置</h2>
              </div>

              <div class="settings-container">
                <div class="card settings-card">
                  <h3 class="settings-section-title">辩论偏好</h3>
                  <form @submit.prevent="savePreferences" class="settings-form">
                    <div class="form-group">
                      <label class="form-label">默认辩论角色</label>
                      <select v-model="preferenceSettings.defaultDebateRole" class="form-select">
                        <option value="spectator">观众</option>
                        <option value="supporter">正方</option>
                        <option value="opposer">反方</option>
                        <option value="judge">裁判</option>
                      </select>
                    </div>

                    <div class="form-group">
                      <label class="form-label">辩论风格</label>
                      <select v-model="preferenceSettings.debateStyle" class="form-select">
                        <option value="academic">学术型</option>
                        <option value="casual">休闲型</option>
                        <option value="competitive">竞技型</option>
                      </select>
                    </div>

                    <div class="form-group">
                      <label class="form-label">语言偏好</label>
                      <select v-model="preferenceSettings.languagePreference" class="form-select">
                        <option value="zh-CN">简体中文</option>
                        <option value="en-US">English</option>
                      </select>
                    </div>

                    <div class="form-group">
                      <label class="form-label">AI模型偏好</label>
                      <select v-model="preferenceSettings.aiModelPreference" class="form-select">
                        <option value="gpt-4">GPT-4</option>
                        <option value="gpt-3.5">GPT-3.5</option>
                        <option value="claude">Claude</option>
                      </select>
                    </div>

                    <div class="form-actions">
                      <button type="submit" class="btn btn--primary" :disabled="loadingPreferences">
                        {{ loadingPreferences ? '保存中...' : '保存设置' }}
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            </div>

            <!-- 通知设置 -->
            <div v-if="activeTab === 'notifications'" class="content-section">
              <div class="section-header">
                <h2 class="section-title">通知设置</h2>
              </div>

              <div class="settings-container">
                <div class="card settings-card">
                  <h3 class="settings-section-title">邮件通知</h3>
                  <form @submit.prevent="saveNotificationSettings" class="settings-form">
                    <div class="settings-group">
                      <div class="settings-option">
                        <label class="switch">
                          <input type="checkbox" v-model="notificationSettings.emailNotifications">
                          <span class="slider"></span>
                        </label>
                        <div class="option-content">
                          <span class="option-title">接收邮件通知</span>
                          <span class="option-description">接收重要的系统通知和更新</span>
                        </div>
                      </div>

                      <div class="settings-option">
                        <label class="switch">
                          <input type="checkbox" v-model="notificationSettings.debateInvitations">
                          <span class="slider"></span>
                        </label>
                        <div class="option-content">
                          <span class="option-title">辩论邀请通知</span>
                          <span class="option-description">当有人邀请您参与辩论时通知</span>
                        </div>
                      </div>

                      <div class="settings-option">
                        <label class="switch">
                          <input type="checkbox" v-model="notificationSettings.debateUpdates">
                          <span class="slider"></span>
                        </label>
                        <div class="option-content">
                          <span class="option-title">辩论更新通知</span>
                          <span class="option-description">参与的辩论有新动态时通知</span>
                        </div>
                      </div>

                      <div class="settings-option">
                        <label class="switch">
                          <input type="checkbox" v-model="notificationSettings.mentionNotifications">
                          <span class="slider"></span>
                        </label>
                        <div class="option-content">
                          <span class="option-title">被提及通知</span>
                          <span class="option-description">在辩论中被提及时通知</span>
                        </div>
                      </div>

                      <div class="settings-option">
                        <label class="switch">
                          <input type="checkbox" v-model="notificationSettings.systemAnnouncements">
                          <span class="slider"></span>
                        </label>
                        <div class="option-content">
                          <span class="option-title">系统公告通知</span>
                          <span class="option-description">接收系统维护和功能更新通知</span>
                        </div>
                      </div>
                    </div>

                    <div class="form-group">
                      <label class="form-label">通知摘要频率</label>
                      <select v-model="notificationSettings.digestFrequency" class="form-select">
                        <option value="realtime">实时</option>
                        <option value="daily">每日</option>
                        <option value="weekly">每周</option>
                      </select>
                    </div>

                    <div class="form-actions">
                      <button type="submit" class="btn btn--primary" :disabled="loadingNotifications">
                        {{ loadingNotifications ? '保存中...' : '保存设置' }}
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            </div>

            <!-- 隐私设置 -->
            <div v-if="activeTab === 'privacy'" class="content-section">
              <div class="section-header">
                <h2 class="section-title">隐私设置</h2>
              </div>

              <div class="settings-container">
                <div class="card settings-card">
                  <h3 class="settings-section-title">个人信息可见性</h3>
                  <form @submit.prevent="savePrivacySettings" class="settings-form">
                    <div class="settings-group">
                      <div class="settings-option">
                        <label class="switch">
                          <input type="checkbox" v-model="privacySettings.showEmail">
                          <span class="slider"></span>
                        </label>
                        <div class="option-content">
                          <span class="option-title">显示邮箱地址</span>
                          <span class="option-description">其他用户可以看到您的邮箱地址</span>
                        </div>
                      </div>

                      <div class="settings-option">
                        <label class="switch">
                          <input type="checkbox" v-model="privacySettings.showStatistics">
                          <span class="slider"></span>
                        </label>
                        <div class="option-content">
                          <span class="option-title">显示辩论统计</span>
                          <span class="option-description">其他用户可以看到您的辩论胜率和统计数据</span>
                        </div>
                      </div>

                      <div class="settings-option">
                        <label class="switch">
                          <input type="checkbox" v-model="privacySettings.showOnlineStatus">
                          <span class="slider"></span>
                        </label>
                        <div class="option-content">
                          <span class="option-title">显示在线状态</span>
                          <span class="option-description">其他用户可以看到您是否在线</span>
                        </div>
                      </div>

                      <div class="settings-option">
                        <label class="switch">
                          <input type="checkbox" v-model="privacySettings.allowDirectMessages">
                          <span class="slider"></span>
                        </label>
                        <div class="option-content">
                          <span class="option-title">允许私信</span>
                          <span class="option-description">其他用户可以向您发送私信</span>
                        </div>
                      </div>

                      <div class="settings-option">
                        <label class="switch">
                          <input type="checkbox" v-model="privacySettings.allowNotifications">
                          <span class="slider"></span>
                        </label>
                        <div class="option-content">
                          <span class="option-title">允许通知</span>
                          <span class="option-description">接收来自系统和其他用户的通知</span>
                        </div>
                      </div>
                    </div>

                    <div class="form-actions">
                      <button type="submit" class="btn btn--primary" :disabled="loadingPrivacy">
                        {{ loadingPrivacy ? '保存中...' : '保存设置' }}
                      </button>
                    </div>

                    <div v-if="privacyMessage" class="form-message" :class="privacySuccess ? 'success' : 'error'">
                      {{ privacyMessage }}
                    </div>
                  </form>
                </div>
              </div>
            </div>

            <!-- 账户安全 -->
            <div v-if="activeTab === 'security'" class="content-section">
              <div class="section-header">
                <h2 class="section-title">账户安全</h2>
              </div>

              <div class="settings-container">
                <div class="card settings-card">
                  <h3 class="settings-section-title">安全设置</h3>
                  <div class="settings-form">
                    <div class="settings-group">
                      <h4 class="settings-group__title">两步验证</h4>
                      <div class="settings-option">
                        <label class="switch">
                          <input type="checkbox" v-model="securitySettings.twoFactorEnabled"
                            @change="toggleTwoFactorAuth">
                          <span class="slider"></span>
                        </label>
                        <div class="option-content">
                          <span class="option-title">启用两步验证</span>
                          <span class="option-description">为您的账户提供额外的安全保护</span>
                        </div>
                      </div>
                    </div>

                    <div class="settings-group">
                      <h4 class="settings-group__title">登录安全</h4>
                      <div class="settings-option">
                        <label class="switch">
                          <input type="checkbox" v-model="securitySettings.loginNotifications">
                          <span class="slider"></span>
                        </label>
                        <div class="option-content">
                          <span class="option-title">异常登录通知</span>
                          <span class="option-description">检测到异常登录时发送通知</span>
                        </div>
                      </div>

                      <div class="form-group">
                        <label class="form-label">会话超时时间（分钟）</label>
                        <select v-model="securitySettings.sessionTimeout" class="form-select">
                          <option value="15">15分钟</option>
                          <option value="30">30分钟</option>
                          <option value="60">1小时</option>
                          <option value="120">2小时</option>
                        </select>
                      </div>
                    </div>

                    <div class="settings-group">
                      <h4 class="settings-group__title">账户信息</h4>
                      <div class="info-list">
                        <div class="info-item">
                          <span class="info-label">上次登录时间：</span>
                          <span class="info-value">{{ formatTime(user.lastLoginTime) }}</span>
                        </div>
                        <div class="info-item">
                          <span class="info-label">注册时间：</span>
                          <span class="info-value">{{ formatTime(user.registrationDate) }}</span>
                        </div>
                        <div class="info-item">
                          <span class="info-label">上次密码修改：</span>
                          <span class="info-value">{{ formatTime(securitySettings.lastPasswordChange) || '从未修改'
                            }}</span>
                        </div>
                      </div>
                    </div>

                    <div class="settings-group">
                      <h4 class="settings-group__title">已登录设备</h4>
                      <div class="device-list" v-if="securitySettings.trustedDevices.length > 0">
                        <div v-for="device in securitySettings.trustedDevices" :key="device.id" class="device-item">
                          <div class="device-icon">
                            <span v-if="device.type === 'mobile'">📱</span>
                            <span v-else>💻</span>
                          </div>
                          <div class="device-info">
                            <div class="device-name">{{ device.name }}</div>
                            <div class="device-meta">{{ device.lastActive }}</div>
                          </div>
                          <button class="btn btn--danger btn--small" @click="revokeDevice(device.id)">
                            移除
                          </button>
                        </div>
                      </div>
                      <div v-else class="empty-state">
                        <p>暂无已登录设备</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 编辑资料模态框 -->
      <div v-if="showEditProfile" class="modal-overlay" @click.self="showEditProfile = false">
        <div class="modal">
          <div class="modal__header">
            <h2 class="modal__title">编辑个人资料</h2>
            <button class="modal__close" @click="showEditProfile = false">×</button>
          </div>

          <div class="modal__body">
            <form @submit.prevent="updateProfile" class="form">
              <div class="form-group">
                <label class="form-label" for="username">用户名</label>
                <input type="text" id="username" class="form-control" v-model="profileForm.username"
                  :class="{ 'is-invalid': profileErrors.username }" required />
                <span v-if="profileErrors.username" class="form-text text-error">
                  {{ profileErrors.username }}
                </span>
              </div>

              <div class="form-group">
                <label class="form-label" for="bio">个人简介</label>
                <textarea id="bio" class="form-control" v-model="profileForm.bio" rows="3"></textarea>
              </div>

              <div class="form-group">
                <label class="form-label">头像</label>
                <div class="avatar-upload">
                  <div class="avatar-preview">
                    <img :src="profileForm.avatar || user.avatar || 'http://placehold.co/150'" alt="头像预览">
                  </div>
                  <input type="file" id="avatarUpload" class="avatar-input" @change="handleAvatarChange"
                    accept="image/*" />
                  <label for="avatarUpload" class="btn btn--outline">选择图片</label>
                </div>
              </div>
            </form>
          </div>

          <div class="modal__footer">
            <button class="btn btn--outline" @click="showEditProfile = false">取消</button>
            <button class="btn btn--primary" @click="updateProfile" :disabled="loadingProfile">
              {{ loadingProfile ? '保存中...' : '保存' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script src="./js/UserCenter.js"></script>

<style lang="scss">
@use './scss/UserCenter.scss';
</style>
