package com.debate_ournament.ai.controller;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;

import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.debate_ournament.config.AudioConfig;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * 音频文件访问控制器
 */
@RestController
@RequestMapping("/audio")
@Tag(name = "音频文件", description = "音频文件访问接口")
public class AudioController {

    private final AudioConfig audioConfig;

    public AudioController(AudioConfig audioConfig) {
        this.audioConfig = audioConfig;
    }

    /**
     * 获取音频文件
     */
    @GetMapping("/{filename}")
    @Operation(summary = "获取音频文件", description = "通过文件名获取音频文件")
    public ResponseEntity<InputStreamResource> getAudioFile(@PathVariable String filename) throws IOException {

        // 验证文件名安全性
        if (!isValidFilename(filename)) {
            return ResponseEntity.badRequest().build();
        }

        // 构建文件路径
        Path audioPath = Paths.get(audioConfig.getStoragePath(), filename);
        File audioFile = audioPath.toFile();

        // 检查文件是否存在
        if (!audioFile.exists() || !audioFile.isFile()) {
            return ResponseEntity.notFound().build();
        }

        // 验证文件格式
        String fileExtension = getFileExtension(filename);
        if (!isSupportedFormat(fileExtension)) {
            return ResponseEntity.badRequest().build();
        }

        // 准备文件流
        FileInputStream fileInputStream = new FileInputStream(audioFile);
        InputStreamResource resource = new InputStreamResource(fileInputStream);

        // 设置响应头
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + filename + "\"");
        headers.add(HttpHeaders.CACHE_CONTROL, "max-age=3600"); // 缓存1小时

        // 根据文件扩展名设置Content-Type
        MediaType mediaType = getMediaType(fileExtension);

        return ResponseEntity.ok()
                .headers(headers)
                .contentLength(audioFile.length())
                .contentType(mediaType)
                .body(resource);
    }

    /**
     * 验证文件名安全性
     */
    private boolean isValidFilename(String filename) {
        if (filename == null || filename.trim().isEmpty()) {
            return false;
        }

        // 检查路径遍历攻击
        if (filename.contains("..") || filename.contains("/") || filename.contains("\\")) {
            return false;
        }

        // 检查文件名长度
        if (filename.length() > 255) {
            return false;
        }

        return true;
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        return lastDotIndex > 0 ? filename.substring(lastDotIndex + 1).toLowerCase() : "";
    }

    /**
     * 检查是否为支持的格式
     */
    private boolean isSupportedFormat(String extension) {
        for (String format : audioConfig.getSupportedFormats()) {
            if (format.equalsIgnoreCase(extension)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 根据文件扩展名获取MediaType
     */
    private MediaType getMediaType(String extension) {
        return switch (extension.toLowerCase()) {
            case "mp3" -> MediaType.parseMediaType("audio/mpeg");
            case "wav" -> MediaType.parseMediaType("audio/wav");
            case "opus" -> MediaType.parseMediaType("audio/opus");
            case "aac" -> MediaType.parseMediaType("audio/aac");
            case "flac" -> MediaType.parseMediaType("audio/flac");
            case "ogg" -> MediaType.parseMediaType("audio/ogg");
            default -> MediaType.APPLICATION_OCTET_STREAM;
        };
    }
}
